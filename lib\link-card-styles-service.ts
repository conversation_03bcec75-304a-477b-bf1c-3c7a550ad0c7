import { supabase } from './supabase';
import { TABLES } from './supabase';

export interface HoverEffect {
  transform?: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  shadowEffect?: string;
  transition?: string;
}

export interface LinkCardStyle {
  $id?: string;
  name: string;
  styleId: string;
  description?: string;
  category: 'glass' | 'neon' | 'minimal' | 'bold' | 'soft' | 'dark' | 'custom';
  cssClasses: string[];
  customCSS?: string;
  previewColor: string;
  borderRadius: string;
  padding: string;
  margin: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  hoverEffect?: HoverEffect;
  shadowEffect?: string;
  isPopular: boolean;
  isDefault: boolean;
  isPremium: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  usageCount: number;
}

export class LinkCardStylesService {
  // Create a new link card style
  async createStyle(styleData: Omit<LinkCardStyle, '$id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<LinkCardStyle> {
    try {
      const now = new Date().toISOString();
      
      const data = {
        ...styleData,
        cssClasses: JSON.stringify(styleData.cssClasses),
        hoverEffect: styleData.hoverEffect ? JSON.stringify(styleData.hoverEffect) : undefined,
        tags: JSON.stringify(styleData.tags),
        createdAt: now,
        updatedAt: now,
        usageCount: 0
      };

      const result = await databases.createDocument(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        ID.unique(),
        data
      );

      return {
        ...result,
        cssClasses: JSON.parse(result.cssClasses),
        hoverEffect: result.hoverEffect ? JSON.parse(result.hoverEffect) : undefined,
        tags: JSON.parse(result.tags || '[]')
      } as LinkCardStyle;
    } catch (error) {
      console.error('Failed to create link card style:', error);
      throw error;
    }
  }

  // Get all link card styles
  async getAllStyles(): Promise<LinkCardStyle[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.orderDesc('isPopular'),
          Query.orderAsc('name'),
          Query.limit(100)
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      })) as LinkCardStyle[];
    } catch (error) {
      console.error('Failed to get link card styles:', error);
      return [];
    }
  }

  // Get styles by category
  async getStylesByCategory(category: string): Promise<LinkCardStyle[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.equal('category', category),
          Query.orderDesc('isPopular'),
          Query.orderAsc('name'),
          Query.limit(50)
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      })) as LinkCardStyle[];
    } catch (error) {
      console.error('Failed to get styles by category:', error);
      return [];
    }
  }

  // Get popular styles
  async getPopularStyles(limit: number = 20): Promise<LinkCardStyle[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.equal('isPopular', true),
          Query.orderDesc('usageCount'),
          Query.limit(limit)
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      })) as LinkCardStyle[];
    } catch (error) {
      console.error('Failed to get popular styles:', error);
      return [];
    }
  }

  // Get default styles
  async getDefaultStyles(): Promise<LinkCardStyle[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.equal('isDefault', true),
          Query.orderAsc('name')
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      })) as LinkCardStyle[];
    } catch (error) {
      console.error('Failed to get default styles:', error);
      return [];
    }
  }

  // Get premium styles
  async getPremiumStyles(): Promise<LinkCardStyle[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.equal('isPremium', true),
          Query.orderDesc('isPopular'),
          Query.orderAsc('name')
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      })) as LinkCardStyle[];
    } catch (error) {
      console.error('Failed to get premium styles:', error);
      return [];
    }
  }

  // Search styles
  async searchStyles(query: string): Promise<LinkCardStyle[]> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.search('name', query),
          Query.limit(20)
        ]
      );

      return result.documents.map(doc => ({
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      })) as LinkCardStyle[];
    } catch (error) {
      console.error('Failed to search styles:', error);
      return [];
    }
  }

  // Increment style usage
  async incrementUsage(styleId: string): Promise<void> {
    try {
      const style = await databases.getDocument(DATABASE_ID, LINK_CARD_STYLES_COLLECTION_ID, styleId);
      await databases.updateDocument(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        styleId,
        {
          usageCount: (style.usageCount || 0) + 1,
          updatedAt: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Failed to increment style usage:', error);
      // Don't throw error for usage tracking
    }
  }

  // Get style by styleId
  async getStyleByStyleId(styleId: string): Promise<LinkCardStyle | null> {
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        LINK_CARD_STYLES_COLLECTION_ID,
        [
          Query.equal('styleId', styleId),
          Query.limit(1)
        ]
      );

      if (result.documents.length === 0) return null;

      const doc = result.documents[0];
      return {
        ...doc,
        cssClasses: JSON.parse(doc.cssClasses || '[]'),
        hoverEffect: doc.hoverEffect ? JSON.parse(doc.hoverEffect) : undefined,
        tags: JSON.parse(doc.tags || '[]')
      } as LinkCardStyle;
    } catch (error) {
      console.error('Failed to get style by styleId:', error);
      return null;
    }
  }

  // Generate CSS string from style
  generateCSS(style: LinkCardStyle): string {
    let css = '';
    
    // Base styles
    css += `
      border-radius: ${style.borderRadius};
      padding: ${style.padding};
      margin: ${style.margin};
    `;

    if (style.backgroundColor) {
      css += `background-color: ${style.backgroundColor};`;
    }

    if (style.borderColor) {
      css += `border-color: ${style.borderColor};`;
    }

    if (style.textColor) {
      css += `color: ${style.textColor};`;
    }

    if (style.shadowEffect) {
      css += `box-shadow: ${style.shadowEffect};`;
    }

    // Custom CSS
    if (style.customCSS) {
      css += style.customCSS;
    }

    return css;
  }

  // Generate hover CSS
  generateHoverCSS(style: LinkCardStyle): string {
    if (!style.hoverEffect) return '';

    let css = '';
    const hover = style.hoverEffect;

    if (hover.transform) css += `transform: ${hover.transform};`;
    if (hover.backgroundColor) css += `background-color: ${hover.backgroundColor};`;
    if (hover.borderColor) css += `border-color: ${hover.borderColor};`;
    if (hover.textColor) css += `color: ${hover.textColor};`;
    if (hover.shadowEffect) css += `box-shadow: ${hover.shadowEffect};`;
    if (hover.transition) css += `transition: ${hover.transition};`;

    return css;
  }

  // Create default link card styles (run once during setup)
  async createDefaultStyles(): Promise<void> {
    const defaultStyles: Omit<LinkCardStyle, '$id' | 'createdAt' | 'updatedAt' | 'usageCount'>[] = [
      // Glass Style
      {
        name: 'Glass',
        styleId: 'glass',
        description: 'Elegant glassmorphism effect with transparency',
        category: 'glass',
        cssClasses: [
          'backdrop-blur-md',
          'bg-white/20',
          'border',
          'border-white/30',
          'rounded-xl',
          'shadow-lg',
          'transition-all',
          'duration-300'
        ],
        previewColor: '#ffffff40',
        borderRadius: '12px',
        padding: '16px',
        margin: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        borderColor: 'rgba(255, 255, 255, 0.3)',
        shadowEffect: '0 8px 32px rgba(31, 38, 135, 0.37)',
        hoverEffect: {
          transform: 'translateY(-2px)',
          backgroundColor: 'rgba(255, 255, 255, 0.3)',
          shadowEffect: '0 12px 40px rgba(31, 38, 135, 0.5)',
          transition: 'all 0.3s ease'
        },
        isPopular: true,
        isDefault: true,
        isPremium: false,
        tags: ['glassmorphism', 'elegant', 'modern', 'transparent']
      },

      // Neon Style
      {
        name: 'Neon',
        styleId: 'neon',
        description: 'Vibrant neon glow effect',
        category: 'neon',
        cssClasses: [
          'bg-black',
          'border-2',
          'border-cyan-400',
          'rounded-lg',
          'shadow-neon',
          'transition-all',
          'duration-300',
          'text-cyan-300'
        ],
        customCSS: `
          box-shadow: 0 0 20px rgba(34, 211, 238, 0.5), inset 0 0 20px rgba(34, 211, 238, 0.1);
        `,
        previewColor: '#22d3ee',
        borderRadius: '8px',
        padding: '16px',
        margin: '8px',
        backgroundColor: '#000000',
        borderColor: '#22d3ee',
        textColor: '#67e8f9',
        shadowEffect: '0 0 20px rgba(34, 211, 238, 0.5)',
        hoverEffect: {
          transform: 'scale(1.02)',
          shadowEffect: '0 0 30px rgba(34, 211, 238, 0.8), inset 0 0 30px rgba(34, 211, 238, 0.2)',
          transition: 'all 0.3s ease'
        },
        isPopular: true,
        isDefault: true,
        isPremium: false,
        tags: ['neon', 'cyberpunk', 'glow', 'futuristic']
      },

      // Minimal Style
      {
        name: 'Minimal',
        styleId: 'minimal',
        description: 'Clean and simple design',
        category: 'minimal',
        cssClasses: [
          'bg-white',
          'border',
          'border-gray-200',
          'rounded-lg',
          'shadow-sm',
          'transition-all',
          'duration-200',
          'hover:shadow-md'
        ],
        previewColor: '#f8f9fa',
        borderRadius: '8px',
        padding: '16px',
        margin: '8px',
        backgroundColor: '#ffffff',
        borderColor: '#e5e7eb',
        textColor: '#374151',
        shadowEffect: '0 1px 3px rgba(0, 0, 0, 0.1)',
        hoverEffect: {
          transform: 'translateY(-1px)',
          shadowEffect: '0 4px 12px rgba(0, 0, 0, 0.15)',
          transition: 'all 0.2s ease'
        },
        isPopular: true,
        isDefault: true,
        isPremium: false,
        tags: ['minimal', 'clean', 'simple', 'professional']
      },

      // Bold Style
      {
        name: 'Bold',
        styleId: 'bold',
        description: 'Strong and impactful design',
        category: 'bold',
        cssClasses: [
          'bg-gradient-to-r',
          'from-purple-600',
          'to-pink-600',
          'text-white',
          'rounded-xl',
          'shadow-xl',
          'transition-all',
          'duration-300',
          'font-bold'
        ],
        previewColor: '#a855f7',
        borderRadius: '12px',
        padding: '20px',
        margin: '8px',
        backgroundColor: 'linear-gradient(to right, #9333ea, #ec4899)',
        textColor: '#ffffff',
        shadowEffect: '0 10px 25px rgba(168, 85, 247, 0.4)',
        hoverEffect: {
          transform: 'translateY(-3px) scale(1.02)',
          shadowEffect: '0 15px 35px rgba(168, 85, 247, 0.6)',
          transition: 'all 0.3s ease'
        },
        isPopular: true,
        isDefault: true,
        isPremium: false,
        tags: ['bold', 'gradient', 'vibrant', 'impactful']
      },

      // Soft Style
      {
        name: 'Soft',
        styleId: 'soft',
        description: 'Gentle and subtle appearance',
        category: 'soft',
        cssClasses: [
          'bg-gray-50',
          'border',
          'border-gray-100',
          'rounded-2xl',
          'shadow-soft',
          'transition-all',
          'duration-300'
        ],
        customCSS: `
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
        `,
        previewColor: '#f9fafb',
        borderRadius: '16px',
        padding: '18px',
        margin: '8px',
        backgroundColor: '#f9fafb',
        borderColor: '#f3f4f6',
        textColor: '#6b7280',
        shadowEffect: '0 4px 20px rgba(0, 0, 0, 0.05)',
        hoverEffect: {
          transform: 'translateY(-2px)',
          backgroundColor: '#ffffff',
          shadowEffect: '0 8px 30px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease'
        },
        isPopular: false,
        isDefault: true,
        isPremium: false,
        tags: ['soft', 'subtle', 'gentle', 'calm']
      },

      // Dark Style
      {
        name: 'Dark',
        styleId: 'dark',
        description: 'Sleek dark theme design',
        category: 'dark',
        cssClasses: [
          'bg-gray-900',
          'border',
          'border-gray-700',
          'rounded-lg',
          'shadow-dark',
          'transition-all',
          'duration-300',
          'text-gray-100'
        ],
        customCSS: `
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.5);
        `,
        previewColor: '#111827',
        borderRadius: '8px',
        padding: '16px',
        margin: '8px',
        backgroundColor: '#111827',
        borderColor: '#374151',
        textColor: '#f9fafb',
        shadowEffect: '0 4px 20px rgba(0, 0, 0, 0.3)',
        hoverEffect: {
          transform: 'translateY(-2px)',
          backgroundColor: '#1f2937',
          borderColor: '#4b5563',
          shadowEffect: '0 8px 30px rgba(0, 0, 0, 0.5)',
          transition: 'all 0.3s ease'
        },
        isPopular: true,
        isDefault: true,
        isPremium: false,
        tags: ['dark', 'sleek', 'modern', 'professional']
      }
    ];

    for (const style of defaultStyles) {
      try {
        await this.createStyle(style);
        console.log(`Created link card style: ${style.name}`);
      } catch (error) {
        console.error(`Failed to create style ${style.name}:`, error);
      }
    }
  }
}

// Export singleton instance
export const linkCardStylesService = new LinkCardStylesService();
