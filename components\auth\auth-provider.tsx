'use client';

import { useEffect, ReactNode, useState } from 'react';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useUserStore } from '@/lib/stores/user-store';

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isClient, setIsClient] = useState(false);
  const { checkAuth, userProfile, isAuthenticated } = useAuthStore();
  const { syncFromAuthProfile } = useUserStore();

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Only run auth check on client side
    if (isClient) {
      console.log('🔐 AuthProvider: Running auth check...');
      checkAuth();
    }
  }, [checkAuth, isClient]);

  useEffect(() => {
    // Sync user profile when authentication changes
    if (isClient && isAuthenticated && userProfile) {
      syncFromAuthProfile(userProfile);
    }
  }, [isAuthenticated, userProfile, syncFromAuthProfile, isClient]);

  // Don't render children until we're on the client
  if (!isClient) {
    return null;
  }

  return <>{children}</>;
}
