import { useCacheStore } from './stores/cache-store';
import { useAuthStore } from './stores/auth-store';
import { useLinksStore } from './stores/links-store';
import { useThemeStore } from './stores/theme-store';
import { useFontsStore } from './stores/fonts-store';
import { useProfileStore } from './stores/profile-store';

// Cache invalidation strategies
export class CacheInvalidationManager {
  private cache = useCacheStore.getState();

  // User-related invalidations
  invalidateUserData(userId: string) {
    console.log('🗑️ Invalidating user data for:', userId);
    
    // Clear cached API data
    this.cache.invalidatePattern(`user:.*:${userId}`);
    
    // Clear store data
    const linksStore = useLinksStore.getState();
    const profileStore = useProfileStore.getState();
    
    if (linksStore.userId === userId) {
      linksStore.clearLinks();
    }
    
    if (profileStore.profile?.user_id === userId) {
      profileStore.clearCache();
    }
  }

  // Profile-related invalidations
  invalidateProfile(userId: string, username?: string) {
    console.log('🗑️ Invalidating profile data for:', userId);
    
    // Clear cached API data
    this.cache.invalidate(`user:profile:${userId}`);
    
    if (username) {
      this.cache.invalidate(`public:profile:${username}`);
    }
    
    // Clear public profiles cache
    this.cache.invalidatePattern(`public:profile:.*`);
    
    // Refresh profile store if it's the current user
    const profileStore = useProfileStore.getState();
    if (profileStore.profile?.user_id === userId) {
      profileStore.refreshProfile();
    }
  }

  // Links-related invalidations
  invalidateLinks(userId: string, username?: string) {
    console.log('🗑️ Invalidating links data for:', userId);
    
    // Clear cached API data
    this.cache.invalidate(`user:links:${userId}`);
    
    if (username) {
      this.cache.invalidate(`public:links:${username}`);
    }
    
    // Refresh links store if it's the current user
    const linksStore = useLinksStore.getState();
    if (linksStore.userId === userId) {
      linksStore.loadLinks(userId, true);
    }
  }

  // Theme-related invalidations
  invalidateThemes(userId?: string) {
    console.log('🗑️ Invalidating themes data');
    
    // Clear cached API data
    this.cache.invalidatePattern(`themes:.*`);
    
    if (userId) {
      this.cache.invalidate(`user:themes:${userId}`);
    }
    
    // Clear theme store cache
    const themeStore = useThemeStore.getState();
    themeStore.reset();
  }

  // Font-related invalidations (rarely needed since fonts are mostly static)
  invalidateFonts() {
    console.log('🗑️ Invalidating fonts data');
    
    // Clear cached API data
    this.cache.invalidatePattern(`fonts:.*`);
    
    // Clear fonts store cache
    const fontsStore = useFontsStore.getState();
    fontsStore.clearCache();
  }

  // Authentication-related invalidations
  invalidateAuthData() {
    console.log('🗑️ Invalidating auth data');
    
    // Clear all user-specific cached data
    this.cache.invalidatePattern(`user:.*`);
    
    // Clear all stores
    useLinksStore.getState().clearLinks();
    useProfileStore.getState().clearCache();
    useThemeStore.getState().reset();
  }

  // Public data invalidations (when user makes profile/links public)
  invalidatePublicData(username: string) {
    console.log('🗑️ Invalidating public data for:', username);
    
    this.cache.invalidate(`public:profile:${username}`);
    this.cache.invalidate(`public:links:${username}`);
    
    // Clear from profile store
    const profileStore = useProfileStore.getState();
    const newPublicProfiles = { ...profileStore.publicProfiles };
    delete newPublicProfiles[username];
    profileStore.setProfile(profileStore.profile);
  }

  // Search-related invalidations
  invalidateSearchResults() {
    console.log('🗑️ Invalidating search results');
    
    this.cache.invalidatePattern(`search:.*`);
    
    // Clear search results from stores
    const fontsStore = useFontsStore.getState();
    fontsStore.clearCache();
  }

  // Complete cache clear (nuclear option)
  clearAllCaches() {
    console.log('💥 Clearing all caches');
    
    // Clear cached API data
    this.cache.clear();
    
    // Clear all stores
    useLinksStore.getState().clearLinks();
    useProfileStore.getState().clearCache();
    useThemeStore.getState().reset();
    useFontsStore.getState().clearCache();
  }

  // Smart invalidation based on data type and operation
  invalidateAfterUpdate(dataType: string, operation: string, data: any) {
    const userId = data.userId || data.user_id;
    const username = data.username;

    switch (dataType) {
      case 'profile':
        this.invalidateProfile(userId, username);
        break;
        
      case 'links':
        this.invalidateLinks(userId, username);
        break;
        
      case 'theme':
        this.invalidateThemes(userId);
        break;
        
      case 'user':
        this.invalidateUserData(userId);
        break;
        
      default:
        console.warn('Unknown data type for cache invalidation:', dataType);
    }
  }

  // Scheduled cleanup (can be called periodically)
  performScheduledCleanup() {
    console.log('🧹 Performing scheduled cache cleanup');
    
    // Clean expired entries
    this.cache.cleanup();
    
    // Get cache stats
    const stats = this.cache.getStats();
    console.log('📊 Cache stats after cleanup:', stats);
    
    // If cache is getting too large, clear old entries
    if (stats.totalEntries > 500) {
      console.log('🗑️ Cache too large, clearing old entries');
      // Could implement more sophisticated LRU cleanup here
    }
  }
}

// Export singleton instance
export const cacheInvalidation = new CacheInvalidationManager();

// Convenience functions
export const invalidateUserData = (userId: string) => 
  cacheInvalidation.invalidateUserData(userId);

export const invalidateProfile = (userId: string, username?: string) => 
  cacheInvalidation.invalidateProfile(userId, username);

export const invalidateLinks = (userId: string, username?: string) => 
  cacheInvalidation.invalidateLinks(userId, username);

export const invalidateThemes = (userId?: string) => 
  cacheInvalidation.invalidateThemes(userId);

export const invalidateAuthData = () => 
  cacheInvalidation.invalidateAuthData();

export const clearAllCaches = () => 
  cacheInvalidation.clearAllCaches();

// Hook for components to trigger invalidations
export const useCacheInvalidation = () => {
  return {
    invalidateUserData,
    invalidateProfile,
    invalidateLinks,
    invalidateThemes,
    invalidateAuthData,
    clearAllCaches,
    performCleanup: () => cacheInvalidation.performScheduledCleanup(),
    getCacheStats: () => useCacheStore.getState().getStats(),
  };
};

// Auto-cleanup interval (runs every 15 minutes)
if (typeof window !== 'undefined') {
  setInterval(() => {
    cacheInvalidation.performScheduledCleanup();
  }, 15 * 60 * 1000);
}
