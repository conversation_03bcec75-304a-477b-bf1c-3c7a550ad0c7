'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/lib/stores/auth-store';
import { supabase } from '@/lib/supabase';
import { Loader2 } from 'lucide-react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { checkAuth } = useAuthStore();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('🔄 Auth callback: Starting authentication check...');

        // Check for OAuth errors in URL
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          console.error('OAuth error:', error, errorDescription);
          router.push(`/auth?error=${error}&description=${errorDescription}`);
          return;
        }

        // Check if we have a session after OAuth
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('🔍 Session check:', { session: !!session, error: sessionError });

        if (sessionError) {
          console.error('Session error:', sessionError);
          router.push('/auth?error=session_failed');
          return;
        }

        if (!session) {
          console.log('⚠️ No session found, waiting longer...');
          // Wait a bit more and try again
          setTimeout(handleAuthCallback, 1000);
          return;
        }

        // Update our auth stores with the new session
        await checkAuth(true); // Force refresh to get the new session

        console.log('✅ Auth callback: Authentication successful');

        // Get redirect URL from query params or default to dashboard
        const redirectTo = searchParams.get('redirect') || '/dashboard';
        router.push(redirectTo);
      } catch (error) {
        console.error('❌ Auth callback failed:', error);
        // Redirect to auth page on error
        router.push('/auth?error=callback_failed');
      }
    };

    // Small delay to ensure OAuth flow is complete and URL is processed
    const timer = setTimeout(handleAuthCallback, 1000);

    return () => clearTimeout(timer);
  }, [checkAuth, router, searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Completing sign in...
        </h2>
        <p className="text-gray-600">
          Please wait while we set up your account.
        </p>
      </div>
    </div>
  );
}
