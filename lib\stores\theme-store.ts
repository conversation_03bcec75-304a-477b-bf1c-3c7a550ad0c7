import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { themesService, Theme, ThemeDesign } from '../themes-service';
import { cachedAPI } from '../cached-api';

interface ThemeState {
  // Current applied theme
  currentTheme: ThemeDesign | null;
  currentThemeId: string | null;
  
  // User's saved themes
  userThemes: Theme[];
  
  // Public themes
  publicThemes: Theme[];
  defaultThemes: Theme[];
  
  // UI state
  isLoading: boolean;
  isSaving: boolean;
  lastFetch: number | null;
  
  // Actions
  setCurrentTheme: (theme: ThemeDesign, themeId?: string) => void;
  saveTheme: (name: string, description?: string, isPublic?: boolean, userId?: string) => Promise<Theme>;
  loadUserThemes: () => Promise<void>;
  loadPublicThemes: (category?: string) => Promise<void>;
  loadDefaultThemes: () => Promise<void>;
  applyTheme: (themeId: string) => Promise<void>;
  deleteTheme: (themeId: string) => Promise<void>;
  updateTheme: (themeId: string, updates: Partial<Theme>) => Promise<void>;
  searchThemes: (query: string) => Promise<Theme[]>;
  reset: () => void;
}

const initialTheme: ThemeDesign = {
  background: { type: 'color', value: 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500', effect: 'none' },
  typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-white' },
  linkCards: { style: 'glass', size: 60 },
  effects: {
    animations: true,
    glassmorphism: true,
    shadows: true
  }
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      currentTheme: initialTheme,
      currentThemeId: null,
      userThemes: [],
      publicThemes: [],
      defaultThemes: [],
      isLoading: false,
      isSaving: false,
      lastFetch: null,

      setCurrentTheme: (theme, themeId) => {
        set({ 
          currentTheme: theme,
          currentThemeId: themeId || null
        });
      },

      saveTheme: async (
        name: string,
        description?: string,
        isPublic: boolean = false,
        userId?: string,
        category: 'minimal' | 'dark' | 'neon' | 'pastel' | 'custom' = 'custom',
        tags: string[] = []
      ) => {
        const { currentTheme } = get();
        if (!currentTheme) throw new Error('No theme to save');
        if (!userId) throw new Error('User ID is required to save theme');

        set({ isSaving: true });
        try {

          const theme = await themesService.saveTheme({
            userId,
            name,
            description,
            design: currentTheme,
            isPublic,
            isDefault: false,
            category,
            tags
          });

          // Add to user themes
          set(state => ({
            userThemes: [theme, ...state.userThemes],
            currentThemeId: theme.$id,
            isSaving: false
          }));

          return theme;
        } catch (error) {
          set({ isSaving: false });
          throw error;
        }
      },

      loadUserThemes: async (userId?: string, forceRefresh = false) => {
        if (!userId) return;

        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

        if (!forceRefresh && state.lastFetch &&
            state.userThemes.length > 0 &&
            (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Themes: Using cached user themes');
          return;
        }

        set({ isLoading: true });
        try {
          const themes = await cachedAPI.getUserThemes(userId, forceRefresh);
          set({
            userThemes: themes,
            isLoading: false,
            lastFetch: now
          });
        } catch (error) {
          set({ isLoading: false });
          console.error('Failed to load user themes:', error);
        }
      },

      loadPublicThemes: async (category?: string, forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

        if (!forceRefresh && state.publicThemes.length > 0 &&
            state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Themes: Using cached public themes');
          return;
        }

        set({ isLoading: true });
        try {
          const themes = await cachedAPI.getPublicThemes(category, forceRefresh);
          set({
            publicThemes: themes,
            isLoading: false,
            lastFetch: now
          });
        } catch (error) {
          set({ isLoading: false });
          console.error('Failed to load public themes:', error);
        }
      },

      loadDefaultThemes: async (forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 2 * 60 * 60 * 1000; // 2 hours

        if (!forceRefresh && state.defaultThemes.length > 0 &&
            state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Themes: Using cached default themes');
          return;
        }

        set({ isLoading: true });
        try {
          const themes = await cachedAPI.getDefaultThemes(forceRefresh);
          set({
            defaultThemes: themes,
            isLoading: false,
            lastFetch: now
          });
        } catch (error) {
          set({ isLoading: false });
          console.error('Failed to load default themes:', error);
        }
      },

      applyTheme: async (themeId: string) => {
        set({ isLoading: true });
        try {
          const theme = await themesService.getTheme(themeId);
          
          // Increment usage count
          await themesService.incrementUsage(themeId);
          
          set({ 
            currentTheme: theme.design,
            currentThemeId: themeId,
            isLoading: false
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      deleteTheme: async (themeId: string) => {
        try {
          await themesService.deleteTheme(themeId);
          
          set(state => ({
            userThemes: state.userThemes.filter(theme => theme.$id !== themeId),
            currentThemeId: state.currentThemeId === themeId ? null : state.currentThemeId
          }));
        } catch (error) {
          throw error;
        }
      },

      updateTheme: async (themeId: string, updates: Partial<Theme>) => {
        try {
          const updatedTheme = await themesService.updateTheme(themeId, updates);
          
          set(state => ({
            userThemes: state.userThemes.map(theme => 
              theme.$id === themeId ? updatedTheme : theme
            )
          }));
        } catch (error) {
          throw error;
        }
      },

      searchThemes: async (query: string) => {
        try {
          const themes = await themesService.searchThemes(query);
          return themes;
        } catch (error) {
          console.error('Failed to search themes:', error);
          return [];
        }
      },

      reset: () => {
        set({
          currentTheme: initialTheme,
          currentThemeId: null,
          userThemes: [],
          publicThemes: [],
          defaultThemes: [],
          isLoading: false,
          isSaving: false
        });
      }
    }),
    {
      name: 'linkvibe-theme-storage',
      partialize: (state) => ({
        currentTheme: state.currentTheme,
        currentThemeId: state.currentThemeId,
        // Don't persist themes arrays - they'll be loaded from server
      }),
      // Use SSR-safe storage
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);
