'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  getStorageQuota, 
  getStorageWarningLevel, 
  cleanupStorage, 
  formatBytes,
  monitorStorageUsage 
} from '@/lib/storage-utils';
import { AlertTriangle, HardDrive, Trash2 } from 'lucide-react';

export function StorageMonitor() {
  const [quota, setQuota] = useState(getStorageQuota());
  const [warningLevel, setWarningLevel] = useState(getStorageWarningLevel());
  const [isCleaningUp, setIsCleaningUp] = useState(false);

  const updateStorageInfo = () => {
    setQuota(getStorageQuota());
    setWarningLevel(getStorageWarningLevel());
  };

  useEffect(() => {
    updateStorageInfo();
    monitorStorageUsage();
    
    // Monitor storage periodically
    const interval = setInterval(() => {
      updateStorageInfo();
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleCleanup = async () => {
    setIsCleaningUp(true);
    try {
      const freedSpace = cleanupStorage(['linkvibe-user-storage', 'linkvibe-auth-storage']);
      console.log(`Cleanup completed, freed ${formatBytes(freedSpace)}`);
      updateStorageInfo();
    } catch (error) {
      console.error('Cleanup failed:', error);
    } finally {
      setIsCleaningUp(false);
    }
  };

  // Only show if there's a warning or critical level
  if (warningLevel === 'safe') {
    return null;
  }

  return (
    <Alert className={`mb-4 ${warningLevel === 'critical' ? 'border-red-500 bg-red-50' : 'border-yellow-500 bg-yellow-50'}`}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between w-full">
        <div className="flex-1">
          <div className="font-medium mb-2">
            {warningLevel === 'critical' ? 'Storage Critical' : 'Storage Warning'}
          </div>
          <div className="text-sm text-muted-foreground mb-2">
            Using {quota.percentage.toFixed(1)}% of available storage ({formatBytes(quota.used)} / {formatBytes(quota.total)})
          </div>
          <Progress 
            value={quota.percentage} 
            className={`w-full h-2 ${warningLevel === 'critical' ? 'bg-red-100' : 'bg-yellow-100'}`}
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleCleanup}
          disabled={isCleaningUp}
          className="ml-4"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          {isCleaningUp ? 'Cleaning...' : 'Clean Up'}
        </Button>
      </AlertDescription>
    </Alert>
  );
}

export function StorageSettings() {
  const [quota, setQuota] = useState(getStorageQuota());
  const [isCleaningUp, setIsCleaningUp] = useState(false);

  const updateStorageInfo = () => {
    setQuota(getStorageQuota());
  };

  useEffect(() => {
    updateStorageInfo();
  }, []);

  const handleCleanup = async () => {
    setIsCleaningUp(true);
    try {
      const freedSpace = cleanupStorage(['linkvibe-user-storage', 'linkvibe-auth-storage']);
      console.log(`Cleanup completed, freed ${formatBytes(freedSpace)}`);
      updateStorageInfo();
    } catch (error) {
      console.error('Cleanup failed:', error);
    } finally {
      setIsCleaningUp(false);
    }
  };

  const handleClearAll = () => {
    if (confirm('Are you sure you want to clear all stored data? This will log you out and reset your preferences.')) {
      localStorage.clear();
      window.location.reload();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <HardDrive className="w-5 h-5 mr-2" />
          Storage Management
        </CardTitle>
        <CardDescription>
          Manage your browser's local storage usage
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between text-sm mb-2">
            <span>Storage Used</span>
            <span>{quota.percentage.toFixed(1)}%</span>
          </div>
          <Progress value={quota.percentage} className="w-full" />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{formatBytes(quota.used)} used</span>
            <span>{formatBytes(quota.available)} available</span>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCleanup}
            disabled={isCleaningUp}
            className="flex-1"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {isCleaningUp ? 'Cleaning...' : 'Clean Up Old Data'}
          </Button>
          
          <Button
            variant="destructive"
            onClick={handleClearAll}
            className="flex-1"
          >
            Clear All Data
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>• Clean Up removes old and large cached data</p>
          <p>• Clear All Data will log you out and reset all preferences</p>
        </div>
      </CardContent>
    </Card>
  );
}
