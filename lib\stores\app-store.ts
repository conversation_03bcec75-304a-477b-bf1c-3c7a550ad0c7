import { create } from 'zustand';

export interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    accent: string;
  };
  isPremium: boolean;
}

export interface AppNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

interface AppState {
  // Templates
  templates: Template[];
  selectedTemplate: string;
  
  // UI State
  isMobilePreview: boolean;
  isPublishing: boolean;
  isSaving: boolean;
  
  // Notifications
  notifications: AppNotification[];
  
  // Modals
  isTemplateModalOpen: boolean;
  isSettingsModalOpen: boolean;
  isAnalyticsModalOpen: boolean;
  
  // Actions
  setSelectedTemplate: (templateId: string) => void;
  setMobilePreview: (isMobile: boolean) => void;
  setPublishing: (isPublishing: boolean) => void;
  setSaving: (isSaving: boolean) => void;
  
  // Notification Actions
  addNotification: (notification: Omit<AppNotification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Modal Actions
  openTemplateModal: () => void;
  closeTemplateModal: () => void;
  openSettingsModal: () => void;
  closeSettingsModal: () => void;
  openAnalyticsModal: () => void;
  closeAnalyticsModal: () => void;
}

const defaultTemplates: Template[] = [
  {
    id: 'minimal',
    name: 'Minimal',
    description: 'Clean and professional',
    preview: 'minimal-preview',
    colors: {
      primary: '#000000',
      secondary: '#666666',
      background: '#ffffff',
      text: '#000000',
      accent: '#f5f5f5',
    },
    isPremium: false,
  },
  {
    id: 'dark',
    name: 'Dark Mode',
    description: 'Sleek and modern',
    preview: 'dark-preview',
    colors: {
      primary: '#ffffff',
      secondary: '#a1a1aa',
      background: '#000000',
      text: '#ffffff',
      accent: '#18181b',
    },
    isPremium: false,
  },
  {
    id: 'neon',
    name: 'Neon',
    description: 'Bold and vibrant',
    preview: 'neon-preview',
    colors: {
      primary: '#00ffff',
      secondary: '#ff00ff',
      background: '#0a0a0a',
      text: '#ffffff',
      accent: '#1a1a2e',
    },
    isPremium: true,
  },
  {
    id: 'pastel',
    name: 'Pastel',
    description: 'Soft and dreamy',
    preview: 'pastel-preview',
    colors: {
      primary: '#fbbf24',
      secondary: '#a78bfa',
      background: '#fef3f2',
      text: '#374151',
      accent: '#f9fafb',
    },
    isPremium: true,
  },
];

export const useAppStore = create<AppState>((set, get) => ({
  // Initial State
  templates: defaultTemplates,
  selectedTemplate: 'minimal',
  isMobilePreview: true,
  isPublishing: false,
  isSaving: false,
  notifications: [],
  isTemplateModalOpen: false,
  isSettingsModalOpen: false,
  isAnalyticsModalOpen: false,
  
  // Template Actions
  setSelectedTemplate: (templateId) => {
    set({ selectedTemplate: templateId });
    
    // Add success notification
    get().addNotification({
      type: 'success',
      title: 'Template Updated',
      message: `Successfully switched to ${get().templates.find(t => t.id === templateId)?.name} template`,
      duration: 3000,
    });
  },
  
  // UI Actions
  setMobilePreview: (isMobile) => set({ isMobilePreview: isMobile }),
  setPublishing: (isPublishing) => set({ isPublishing }),
  setSaving: (isSaving) => set({ isSaving }),
  
  // Notification Actions
  addNotification: (notification) => {
    // Create a unique ID using timestamp + random string to avoid collisions
    const id = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const newNotification = { ...notification, id };

    set((state) => ({
      notifications: [...state.notifications, newNotification],
    }));

    // Auto-remove notification after duration
    if (notification.duration) {
      setTimeout(() => {
        get().removeNotification(id);
      }, notification.duration);
    }
  },
  
  removeNotification: (id) => set((state) => ({
    notifications: state.notifications.filter((n) => n.id !== id),
  })),
  
  clearNotifications: () => set({ notifications: [] }),
  
  // Modal Actions
  openTemplateModal: () => set({ isTemplateModalOpen: true }),
  closeTemplateModal: () => set({ isTemplateModalOpen: false }),
  openSettingsModal: () => set({ isSettingsModalOpen: true }),
  closeSettingsModal: () => set({ isSettingsModalOpen: false }),
  openAnalyticsModal: () => set({ isAnalyticsModalOpen: true }),
  closeAnalyticsModal: () => set({ isAnalyticsModalOpen: false }),
}));
