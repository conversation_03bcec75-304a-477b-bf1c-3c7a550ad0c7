'use client';

import React from 'react';
import { useUserData, useStaticData, useVisibilityRefresh } from '@/lib/hooks/use-cached-data';
import { useLinksStore } from '@/lib/stores/links-store';
import { useProfileStore } from '@/lib/stores/profile-store';
import { useCacheInvalidation } from '@/lib/cache-invalidation';
import { 
  CachedDataWrapper, 
  SmartLoading, 
  LinkSkeleton, 
  ProfileSkeleton,
  EmptyState 
} from '@/components/ui/loading-states';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, RefreshCw } from 'lucide-react';

/**
 * Example component demonstrating best practices for using the caching system
 * This shows how to:
 * 1. Load cached data efficiently
 * 2. Handle loading states gracefully
 * 3. Implement optimistic updates
 * 4. Manage cache invalidation
 * 5. Provide fallbacks and error handling
 */
export const CachedPageExample = () => {
  // Load user data with caching
  const { profile, links, isLoading, error, refresh } = useUserData();
  
  // Load static data (fonts, themes) with caching
  const { fonts, themes, isLoading: isStaticLoading } = useStaticData();
  
  // Get store instances for direct operations
  const linksStore = useLinksStore();
  const profileStore = useProfileStore();
  
  // Cache invalidation utilities
  const { invalidateUserData, getCacheStats } = useCacheInvalidation();
  
  // Auto-refresh when page becomes visible
  useVisibilityRefresh(refresh, 5 * 60 * 1000); // 5 minutes

  // Handle adding a new link with optimistic updates
  const handleAddLink = async () => {
    if (!profile?.user_id) return;

    try {
      await linksStore.addLink(profile.user_id, {
        title: 'New Link',
        url: 'https://example.com',
        is_active: true,
        order: links.length
      });
    } catch (error) {
      console.error('Failed to add link:', error);
      // Error handling is built into the store
    }
  };

  // Handle profile update with optimistic updates
  const handleUpdateProfile = async (updates: any) => {
    if (!profile?.id) return;

    try {
      await profileStore.updateProfile(updates);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  // Force refresh all data
  const handleForceRefresh = () => {
    refresh();
    invalidateUserData(profile?.user_id || '');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header with cache stats */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Cached Data Example</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleForceRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh All
          </Button>
          <Button variant="outline" onClick={() => console.log(getCacheStats())}>
            Cache Stats
          </Button>
        </div>
      </div>

      {/* Profile Section */}
      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <CachedDataWrapper
            isLoading={isLoading && !profile}
            error={error}
            data={profile}
            loadingSkeleton={<ProfileSkeleton />}
            emptyState={<EmptyState title="No profile found" />}
            onRetry={refresh}
          >
            <SmartLoading 
              isLoading={profileStore.isLoading} 
              hasData={!!profile}
            >
              {profile && (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                      {profile.avatar ? (
                        <img 
                          src={profile.avatar} 
                          alt={profile.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-xl font-bold">
                          {profile.name.charAt(0)}
                        </span>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{profile.name}</h3>
                      <p className="text-gray-600">@{profile.username}</p>
                      {profile.bio && <p className="text-sm text-gray-500">{profile.bio}</p>}
                    </div>
                  </div>
                  
                  {/* Show unsaved changes indicator */}
                  {profileStore.hasUnsavedChanges && (
                    <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
                      You have unsaved changes
                    </div>
                  )}
                  
                  {/* Show saving indicator */}
                  {profileStore.isSaving && (
                    <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                      Saving changes...
                    </div>
                  )}
                </div>
              )}
            </SmartLoading>
          </CachedDataWrapper>
        </CardContent>
      </Card>

      {/* Links Section */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Links ({links.length})</CardTitle>
            <Button onClick={handleAddLink} disabled={linksStore.isSaving}>
              <Plus className="w-4 h-4 mr-2" />
              Add Link
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <CachedDataWrapper
            isLoading={isLoading && links.length === 0}
            error={error}
            data={links}
            loadingSkeleton={
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <LinkSkeleton key={i} />
                ))}
              </div>
            }
            emptyState={
              <EmptyState 
                title="No links yet" 
                description="Add your first link to get started"
                action={
                  <Button onClick={handleAddLink}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add First Link
                  </Button>
                }
              />
            }
            onRetry={refresh}
          >
            <SmartLoading 
              isLoading={linksStore.isLoading} 
              hasData={links.length > 0}
            >
              <div className="space-y-4">
                {links.map((link) => (
                  <div 
                    key={link.id} 
                    className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      {link.icon ? (
                        <img src={link.icon} alt="" className="w-6 h-6" />
                      ) : (
                        <span className="text-blue-600 font-bold">
                          {link.title.charAt(0)}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{link.title}</h4>
                      <p className="text-sm text-gray-600">{link.url}</p>
                      <p className="text-xs text-gray-500">
                        {link.clicks} clicks • {link.is_active ? 'Active' : 'Inactive'}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => linksStore.toggleLinkActive(link.id!)}
                    >
                      {link.is_active ? 'Deactivate' : 'Activate'}
                    </Button>
                  </div>
                ))}
              </div>
            </SmartLoading>
          </CachedDataWrapper>
        </CardContent>
      </Card>

      {/* Static Data Section */}
      <Card>
        <CardHeader>
          <CardTitle>Static Data</CardTitle>
        </CardHeader>
        <CardContent>
          <CachedDataWrapper
            isLoading={isStaticLoading}
            data={fonts.popular}
            loadingSkeleton={<div className="animate-pulse h-20 bg-gray-200 rounded" />}
          >
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Popular Fonts ({fonts.popular.length})</h4>
                <div className="text-sm text-gray-600">
                  {fonts.popular.slice(0, 3).map(font => font.display_name).join(', ')}
                  {fonts.popular.length > 3 && '...'}
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Default Themes ({themes.default.length})</h4>
                <div className="text-sm text-gray-600">
                  {themes.default.slice(0, 3).map(theme => theme.name).join(', ')}
                  {themes.default.length > 3 && '...'}
                </div>
              </div>
            </div>
          </CachedDataWrapper>
        </CardContent>
      </Card>

      {/* Cache Information */}
      <Card>
        <CardHeader>
          <CardTitle>Cache Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p>• Profile cached: {profile ? '✅' : '❌'}</p>
            <p>• Links cached: {links.length > 0 ? '✅' : '❌'}</p>
            <p>• Fonts cached: {fonts.popular.length > 0 ? '✅' : '❌'}</p>
            <p>• Auto-refresh: Every 5 minutes when page is visible</p>
            <p>• Optimistic updates: Enabled for all operations</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
