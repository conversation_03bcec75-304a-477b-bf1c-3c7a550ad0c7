import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { cachedAPI } from '../cached-api';
import { UserProfile } from '../user-service';

interface ProfileState {
  // Data
  profile: UserProfile | null;
  publicProfiles: Record<string, UserProfile>; // username -> profile
  
  // State
  isLoading: boolean;
  isSaving: boolean;
  lastFetch: number | null;
  hasUnsavedChanges: boolean;
  
  // Actions
  loadProfile: (userId: string, forceRefresh?: boolean) => Promise<void>;
  loadPublicProfile: (username: string, forceRefresh?: boolean) => Promise<UserProfile | null>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  setProfile: (profile: UserProfile | null) => void;
  markUnsavedChanges: (hasChanges: boolean) => void;
  
  // Background/Avatar handling
  updateAvatar: (avatarUrl: string) => Promise<void>;
  updateBackground: (backgroundUrl: string, backgroundType: 'color' | 'image') => Promise<void>;
  
  // Cache management
  clearCache: () => void;
  refreshProfile: () => Promise<void>;
}

export const useProfileStore = create<ProfileState>()(
  persist(
    (set, get) => ({
      // Initial state
      profile: null,
      publicProfiles: {},
      isLoading: false,
      isSaving: false,
      lastFetch: null,
      hasUnsavedChanges: false,

      loadProfile: async (userId: string, forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

        // Skip loading if recently fetched (unless forced)
        if (!forceRefresh && state.profile && 
            state.profile.user_id === userId &&
            state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Profile: Using cached profile');
          return;
        }

        set({ isLoading: true });
        try {
          console.log('🔄 Loading user profile:', userId);
          const profile = await cachedAPI.getUserProfile(userId, forceRefresh);
          
          set({
            profile,
            isLoading: false,
            lastFetch: now,
            hasUnsavedChanges: false
          });
          
          console.log('✅ Profile loaded:', profile?.username);
        } catch (error) {
          console.error('❌ Failed to load profile:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      loadPublicProfile: async (username: string, forceRefresh = false) => {
        const state = get();
        const cached = state.publicProfiles[username];
        
        if (!forceRefresh && cached) {
          console.log(`📦 Profile: Using cached public profile for ${username}`);
          return cached;
        }

        try {
          console.log(`🔄 Loading public profile: ${username}`);
          const profile = await cachedAPI.getPublicProfile(username, forceRefresh);
          
          if (profile) {
            set((state) => ({
              publicProfiles: {
                ...state.publicProfiles,
                [username]: profile
              }
            }));
          }
          
          console.log(`✅ Public profile loaded: ${username}`);
          return profile;
        } catch (error) {
          console.error(`❌ Failed to load public profile for ${username}:`, error);
          throw error;
        }
      },

      updateProfile: async (updates: Partial<UserProfile>) => {
        const state = get();
        if (!state.profile) {
          throw new Error('No profile to update');
        }

        set({ isSaving: true });
        try {
          console.log('📝 Updating profile:', Object.keys(updates));
          
          // Optimistically update the UI
          const updatedProfile = { ...state.profile, ...updates };
          set({ profile: updatedProfile });
          
          // Save to backend
          const savedProfile = await cachedAPI.updateUserProfile(state.profile.id!, updates);
          
          set({
            profile: savedProfile,
            isSaving: false,
            hasUnsavedChanges: false,
            lastFetch: Date.now()
          });
          
          console.log('✅ Profile updated successfully');
        } catch (error) {
          console.error('❌ Failed to update profile:', error);
          // Revert optimistic update on error
          set({ 
            profile: state.profile,
            isSaving: false 
          });
          throw error;
        }
      },

      setProfile: (profile) => {
        set({ 
          profile,
          hasUnsavedChanges: false,
          lastFetch: profile ? Date.now() : null
        });
      },

      markUnsavedChanges: (hasChanges) => {
        set({ hasUnsavedChanges: hasChanges });
      },

      updateAvatar: async (avatarUrl: string) => {
        await get().updateProfile({ avatar: avatarUrl });
      },

      updateBackground: async (backgroundUrl: string, backgroundType: 'color' | 'image') => {
        const updates: Partial<UserProfile> = {
          background_type: backgroundType
        };

        if (backgroundType === 'image') {
          updates.background_image = backgroundUrl;
          updates.background_color = ''; // Clear color when using image
        } else {
          updates.background_color = backgroundUrl;
          updates.background_image = ''; // Clear image when using color
        }

        await get().updateProfile(updates);
      },

      clearCache: () => {
        set({
          profile: null,
          publicProfiles: {},
          lastFetch: null,
          hasUnsavedChanges: false
        });
      },

      refreshProfile: async () => {
        const state = get();
        if (!state.profile?.user_id) return;
        
        await get().loadProfile(state.profile.user_id, true);
      },
    }),
    {
      name: 'linkvibe-profile-storage',
      partialize: (state) => ({
        profile: state.profile,
        publicProfiles: state.publicProfiles,
        lastFetch: state.lastFetch,
        // Don't persist loading states or unsaved changes
      }),
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);

// Helper hooks for common operations
export const useCurrentProfile = () => {
  const profile = useProfileStore((state) => state.profile);
  const isLoading = useProfileStore((state) => state.isLoading);
  const loadProfile = useProfileStore((state) => state.loadProfile);
  
  return { profile, isLoading, loadProfile };
};

export const usePublicProfile = (username: string) => {
  const publicProfiles = useProfileStore((state) => state.publicProfiles);
  const loadPublicProfile = useProfileStore((state) => state.loadPublicProfile);
  
  const profile = publicProfiles[username];
  
  return { 
    profile, 
    loadProfile: () => loadPublicProfile(username),
    isLoading: false // Could add per-profile loading state if needed
  };
};

export const useProfileUpdates = () => {
  const updateProfile = useProfileStore((state) => state.updateProfile);
  const updateAvatar = useProfileStore((state) => state.updateAvatar);
  const updateBackground = useProfileStore((state) => state.updateBackground);
  const isSaving = useProfileStore((state) => state.isSaving);
  const hasUnsavedChanges = useProfileStore((state) => state.hasUnsavedChanges);
  const markUnsavedChanges = useProfileStore((state) => state.markUnsavedChanges);
  
  return {
    updateProfile,
    updateAvatar,
    updateBackground,
    isSaving,
    hasUnsavedChanges,
    markUnsavedChanges
  };
};
