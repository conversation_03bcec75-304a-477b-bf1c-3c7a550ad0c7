# Supabase Setup Guide for LinkVibe

This guide will help you set up Supabase for authentication, database, and storage in your LinkVibe application.

## 1. Create Supabase Account

1. Go to [Supabase](https://supabase.com)
2. Create a new account or sign in
3. Create a new project
4. Choose a region close to your users
5. Set a strong database password

## 2. Project Configuration

### Get Project Details
1. In your Supabase dashboard, go to **Settings > API**
2. Note down your:
   - **Project URL** (e.g., `https://your-project.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

## 3. Database Setup

The database tables have already been created automatically during the migration. They include:

### Tables Created:
- **users** - User profiles and settings
- **links** - User links with styling options
- **analytics** - Link click tracking
- **themes** - Custom themes and templates
- **fonts** - Available fonts
- **link_card_styles** - Predefined link card styles

### Row Level Security (RLS)
RLS policies have been automatically configured to ensure:
- Users can only access their own data
- Public profiles are viewable by anyone
- Fonts and link card styles are readable by all users

## 4. Authentication Setup

### Enable Google OAuth (Optional)
1. Go to **Authentication > Providers** in Supabase dashboard
2. Enable Google provider
3. Add your Google OAuth credentials:
   - **Client ID** from Google Cloud Console
   - **Client Secret** from Google Cloud Console
4. Add authorized redirect URIs:
   - `https://your-project.supabase.co/auth/v1/callback`
   - `http://localhost:3000/auth/callback` (for development)

### Configure Auth Settings
1. Go to **Authentication > Settings**
2. Set **Site URL** to your production domain
3. Add redirect URLs for development and production
4. Configure email templates if needed

## 5. Storage Setup

Storage buckets have been automatically created:

### Buckets:
- **avatars** - User avatar images (10MB limit)
- **backgrounds** - Background images (10MB limit)

### Storage Policies
Policies are configured to allow:
- Users to upload/update/delete their own files
- Public read access to all files

## 6. Environment Variables

Create a `.env.local` file in your project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

Replace the values with your actual Supabase project details.

## 7. Testing

1. Start your development server: `npm run dev`
2. Navigate to `http://localhost:3000/auth`
3. Test email/password registration and login
4. Test Google OAuth login (if configured)
5. Verify that authentication state persists across page refreshes
6. Test file uploads for avatars and backgrounds

## 8. Production Setup

For production deployment:

1. **Update Environment Variables**
   - Set production Supabase URL and keys
   - Configure proper redirect URLs

2. **Security Checklist**
   - Verify RLS policies are working
   - Test that users can only access their own data
   - Ensure file upload limits are enforced
   - Check that public profiles work correctly

3. **Performance Optimization**
   - Enable database connection pooling if needed
   - Configure CDN for static assets
   - Set up proper caching headers

## 9. Database Migrations

If you need to make schema changes:

1. Use Supabase CLI or dashboard SQL editor
2. Always test migrations on a staging environment first
3. Backup your database before major changes

## 10. Monitoring

Set up monitoring for:
- Database performance
- Storage usage
- Authentication metrics
- Error tracking

## Troubleshooting

### Common Issues:

1. **Authentication not working**
   - Check environment variables
   - Verify redirect URLs
   - Ensure RLS policies allow access

2. **File uploads failing**
   - Check storage bucket policies
   - Verify file size limits
   - Ensure proper CORS configuration

3. **Database queries failing**
   - Check RLS policies
   - Verify table permissions
   - Review query syntax

### Getting Help:
- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

## Next Steps

After setting up Supabase:

1. Test all authentication flows
2. Verify database operations work correctly
3. Test file upload functionality
4. Set up proper error handling
5. Configure monitoring and analytics
6. Plan for scaling and backup strategies

Your LinkVibe application is now ready to use Supabase as its backend!
