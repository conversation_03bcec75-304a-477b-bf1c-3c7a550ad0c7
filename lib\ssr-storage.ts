/**
 * SSR-safe storage utility for Zustand persistence
 * Handles localStorage access safely in both client and server environments
 */

export interface SSRStorage {
  getItem: (name: string) => string | null;
  setItem: (name: string, value: string) => void;
  removeItem: (name: string) => void;
}



/**
 * Simple SSR-safe storage that just wraps localStorage
 */
function createSSRSafeStorage(): SSRStorage {
  return {
    getItem: (name: string) => {
      if (typeof window === 'undefined') {
        return null;
      }
      try {
        return localStorage.getItem(name);
      } catch (error) {
        console.warn(`Failed to get localStorage item "${name}":`, error);
        return null;
      }
    },
    setItem: (name: string, value: string) => {
      if (typeof window === 'undefined') {
        return;
      }

      try {
        // Ensure value is a string
        const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
        localStorage.setItem(name, stringValue);
      } catch (error) {
        if (error.name === 'QuotaExceededError') {
          console.warn(`localStorage quota exceeded for "${name}"`);
          // Try to clear some space
          try {
            localStorage.removeItem(name);
            // Try to save a minimal version
            const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
            const parsed = JSON.parse(stringValue);
            const minimal = { timestamp: Date.now() }; // Just save timestamp as fallback
            localStorage.setItem(name, JSON.stringify(minimal));
          } catch (retryError) {
            console.error(`Failed to save minimal data for "${name}":`, retryError);
          }
        } else {
          console.error(`Failed to save to localStorage "${name}":`, error);
        }
      }
    },
    removeItem: (name: string) => {
      if (typeof window === 'undefined') {
        return;
      }
      try {
        localStorage.removeItem(name);
      } catch (error) {
        console.warn(`Failed to remove localStorage item "${name}":`, error);
      }
    }
  };
}

/**
 * Auth storage with SSR safety
 */
export const authStorage = createSSRSafeStorage();

/**
 * User storage with SSR safety
 */
export const userStorage = createSSRSafeStorage();

/**
 * Theme storage with SSR safety
 */
export const themeStorage = createSSRSafeStorage();

/**
 * Hook to check if we're in a browser environment
 */
export function useIsClient() {
  return typeof window !== 'undefined';
}

/**
 * Hook to safely access localStorage
 */
export function useLocalStorage() {
  const isClient = useIsClient();
  
  return {
    getItem: (key: string) => {
      if (!isClient) return null;
      try {
        return localStorage.getItem(key);
      } catch {
        return null;
      }
    },
    setItem: (key: string, value: string) => {
      if (!isClient) return;
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.warn('Failed to set localStorage item:', error);
      }
    },
    removeItem: (key: string) => {
      if (!isClient) return;
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn('Failed to remove localStorage item:', error);
      }
    }
  };
}
