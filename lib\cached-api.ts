import { useCacheStore, CacheKeys, CacheTTL } from './stores/cache-store';
import { userService } from './user-service';
import { linksService } from './links-service';
import { fontsService } from './fonts-service';
import { themesService } from './themes-service';

// Generic cached API wrapper
class CachedAPI {
  private cache = useCacheStore.getState();

  // Generic method to wrap any async function with caching
  async withCache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = CacheTTL.MEDIUM,
    forceRefresh: boolean = false
  ): Promise<T> {
    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.cache.get<T>(key);
      if (cached !== null) {
        console.log(`📦 Cache HIT for key: ${key}`);
        return cached;
      }
    }

    console.log(`🌐 Cache MISS for key: ${key}, fetching...`);
    
    try {
      const data = await fetcher();
      this.cache.set(key, data, ttl);
      console.log(`✅ Cached data for key: ${key}`);
      return data;
    } catch (error) {
      console.error(`❌ Failed to fetch data for key: ${key}`, error);
      throw error;
    }
  }

  // User Profile Methods
  async getUserProfile(userId: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.userProfile(userId),
      () => userService.getUserProfile(userId),
      CacheTTL.USER_DATA,
      forceRefresh
    );
  }

  async getOrCreateUserProfile(authUser: any, forceRefresh = false) {
    const key = CacheKeys.userProfile(authUser.id);
    return this.withCache(
      key,
      () => userService.getOrCreateUserProfile(authUser),
      CacheTTL.USER_DATA,
      forceRefresh
    );
  }

  async updateUserProfile(profileId: string, updates: any) {
    const result = await userService.updateUserProfile(profileId, updates);
    
    // Invalidate related cache entries
    this.cache.invalidatePattern(`user:profile:.*`);
    this.cache.invalidatePattern(`public:profile:.*`);
    
    return result;
  }

  // Links Methods
  async getUserLinks(userId: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.userLinks(userId),
      () => linksService.getUserLinks(userId),
      CacheTTL.USER_DATA,
      forceRefresh
    );
  }

  async createLink(userId: string, linkData: any) {
    const result = await linksService.createLink(userId, linkData);
    
    // Invalidate user links cache
    this.cache.invalidate(CacheKeys.userLinks(userId));
    this.cache.invalidatePattern(`public:links:.*`);
    
    return result;
  }

  async updateLink(linkId: string, updates: any) {
    const result = await linksService.updateLink(linkId, updates);
    
    // Invalidate all user links caches (we don't know which user this belongs to)
    this.cache.invalidatePattern(`user:links:.*`);
    this.cache.invalidatePattern(`public:links:.*`);
    
    return result;
  }

  async deleteLink(linkId: string) {
    const result = await linksService.deleteLink(linkId);
    
    // Invalidate all user links caches
    this.cache.invalidatePattern(`user:links:.*`);
    this.cache.invalidatePattern(`public:links:.*`);
    
    return result;
  }

  // Fonts Methods
  async getAllFonts(forceRefresh = false) {
    return this.withCache(
      CacheKeys.fonts(),
      () => fontsService.getAllFonts(),
      CacheTTL.STATIC_DATA,
      forceRefresh
    );
  }

  async getFontsByCategory(category: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.fonts(category),
      () => fontsService.getFontsByCategory(category),
      CacheTTL.STATIC_DATA,
      forceRefresh
    );
  }

  async getPopularFonts(limit = 20, forceRefresh = false) {
    return this.withCache(
      `fonts:popular:${limit}`,
      () => fontsService.getPopularFonts(limit),
      CacheTTL.STATIC_DATA,
      forceRefresh
    );
  }

  async searchFonts(query: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.searchFonts(query),
      () => fontsService.searchFonts(query),
      CacheTTL.SHORT, // Search results expire quickly
      forceRefresh
    );
  }

  // Themes Methods
  async getUserThemes(userId: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.userThemes(userId),
      () => themesService.getUserThemes(userId),
      CacheTTL.USER_DATA,
      forceRefresh
    );
  }

  async getPublicThemes(category?: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.publicThemes(category),
      () => themesService.getPublicThemes(category),
      CacheTTL.LONG,
      forceRefresh
    );
  }

  async getDefaultThemes(forceRefresh = false) {
    return this.withCache(
      CacheKeys.defaultThemes(),
      () => themesService.getDefaultThemes(),
      CacheTTL.VERY_LONG,
      forceRefresh
    );
  }

  async searchThemes(query: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.searchThemes(query),
      () => themesService.searchThemes(query),
      CacheTTL.SHORT,
      forceRefresh
    );
  }

  async createTheme(themeData: any) {
    const result = await themesService.createTheme(themeData);
    
    // Invalidate theme caches
    this.cache.invalidatePattern(`themes:.*`);
    this.cache.invalidatePattern(`user:themes:.*`);
    
    return result;
  }

  // Public Profile Methods (for bio pages)
  async getPublicProfile(username: string, forceRefresh = false) {
    return this.withCache(
      CacheKeys.publicProfile(username),
      () => userService.getUserProfileByUsername(username),
      CacheTTL.MEDIUM,
      forceRefresh
    );
  }

  async getPublicLinks(username: string, forceRefresh = false) {
    // First get the user profile to get the user ID
    const profile = await this.getPublicProfile(username, forceRefresh);
    if (!profile) return [];

    return this.withCache(
      CacheKeys.publicLinks(username),
      () => linksService.getPublicUserLinks(profile.user_id),
      CacheTTL.MEDIUM,
      forceRefresh
    );
  }

  // Cache management methods
  invalidateUserData(userId: string) {
    this.cache.invalidate(CacheKeys.userProfile(userId));
    this.cache.invalidate(CacheKeys.userLinks(userId));
    this.cache.invalidate(CacheKeys.userThemes(userId));
    this.cache.invalidate(CacheKeys.userAnalytics(userId));
  }

  invalidateAllUserData() {
    this.cache.invalidatePattern(`user:.*`);
  }

  invalidatePublicData() {
    this.cache.invalidatePattern(`public:.*`);
  }

  clearAllCache() {
    this.cache.clear();
  }

  getCacheStats() {
    return this.cache.getStats();
  }
}

// Export singleton instance
export const cachedAPI = new CachedAPI();

// Export individual methods for convenience
export const {
  getUserProfile,
  getOrCreateUserProfile,
  updateUserProfile,
  getUserLinks,
  createLink,
  updateLink,
  deleteLink,
  getAllFonts,
  getFontsByCategory,
  getPopularFonts,
  searchFonts,
  getUserThemes,
  getPublicThemes,
  getDefaultThemes,
  searchThemes,
  createTheme,
  getPublicProfile,
  getPublicLinks,
  invalidateUserData,
  invalidateAllUserData,
  invalidatePublicData,
  clearAllCache,
  getCacheStats,
} = cachedAPI;
