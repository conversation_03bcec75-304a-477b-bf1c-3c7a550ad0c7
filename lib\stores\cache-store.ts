import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Cache entry interface
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  key: string;
}

// Cache configuration
interface CacheConfig {
  defaultTTL: number;
  maxEntries: number;
  cleanupInterval: number;
}

interface CacheState {
  entries: Record<string, CacheEntry>;
  config: CacheConfig;
  
  // Actions
  set: <T>(key: string, data: T, ttl?: number) => void;
  get: <T>(key: string) => T | null;
  has: (key: string) => boolean;
  invalidate: (key: string) => void;
  invalidatePattern: (pattern: string) => void;
  clear: () => void;
  cleanup: () => void;
  getStats: () => { totalEntries: number; validEntries: number; expiredEntries: number };
}

const DEFAULT_CONFIG: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxEntries: 1000,
  cleanupInterval: 10 * 60 * 1000, // 10 minutes
};

export const useCacheStore = create<CacheState>()(
  persist(
    (set, get) => ({
      entries: {},
      config: DEFAULT_CONFIG,

      set: <T>(key: string, data: T, ttl?: number) => {
        const now = Date.now();
        const entry: CacheEntry<T> = {
          data,
          timestamp: now,
          ttl: ttl || get().config.defaultTTL,
          key,
        };

        set((state) => {
          const newEntries = { ...state.entries, [key]: entry };
          
          // Check if we need to cleanup old entries
          const entryCount = Object.keys(newEntries).length;
          if (entryCount > state.config.maxEntries) {
            // Remove oldest entries
            const sortedEntries = Object.entries(newEntries)
              .sort(([, a], [, b]) => a.timestamp - b.timestamp)
              .slice(entryCount - state.config.maxEntries);
            
            const cleanedEntries = Object.fromEntries(sortedEntries);
            return { entries: cleanedEntries };
          }
          
          return { entries: newEntries };
        });
      },

      get: <T>(key: string): T | null => {
        const entry = get().entries[key] as CacheEntry<T> | undefined;
        
        if (!entry) {
          return null;
        }

        const now = Date.now();
        const isExpired = now - entry.timestamp > entry.ttl;

        if (isExpired) {
          // Remove expired entry
          get().invalidate(key);
          return null;
        }

        return entry.data;
      },

      has: (key: string): boolean => {
        const entry = get().entries[key];
        if (!entry) return false;

        const now = Date.now();
        const isExpired = now - entry.timestamp > entry.ttl;

        if (isExpired) {
          get().invalidate(key);
          return false;
        }

        return true;
      },

      invalidate: (key: string) => {
        set((state) => {
          const newEntries = { ...state.entries };
          delete newEntries[key];
          return { entries: newEntries };
        });
      },

      invalidatePattern: (pattern: string) => {
        set((state) => {
          const regex = new RegExp(pattern);
          const newEntries = Object.fromEntries(
            Object.entries(state.entries).filter(([key]) => !regex.test(key))
          );
          return { entries: newEntries };
        });
      },

      clear: () => {
        set({ entries: {} });
      },

      cleanup: () => {
        const now = Date.now();
        set((state) => {
          const newEntries = Object.fromEntries(
            Object.entries(state.entries).filter(([, entry]) => {
              return now - entry.timestamp <= entry.ttl;
            })
          );
          return { entries: newEntries };
        });
      },

      getStats: () => {
        const entries = Object.values(get().entries);
        const now = Date.now();
        
        const validEntries = entries.filter(entry => 
          now - entry.timestamp <= entry.ttl
        ).length;
        
        return {
          totalEntries: entries.length,
          validEntries,
          expiredEntries: entries.length - validEntries,
        };
      },
    }),
    {
      name: 'linkvibe-cache-storage',
      partialize: (state) => ({
        entries: state.entries,
        // Don't persist config - use defaults on reload
      }),
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);

// Cache key generators
export const CacheKeys = {
  // User data
  userProfile: (userId: string) => `user:profile:${userId}`,
  userLinks: (userId: string) => `user:links:${userId}`,
  userThemes: (userId: string) => `user:themes:${userId}`,
  userAnalytics: (userId: string) => `user:analytics:${userId}`,
  
  // Public data
  publicThemes: (category?: string) => `themes:public${category ? `:${category}` : ''}`,
  defaultThemes: () => 'themes:default',
  fonts: (category?: string) => `fonts${category ? `:${category}` : ''}`,
  linkCardStyles: (category?: string) => `link-card-styles${category ? `:${category}` : ''}`,
  
  // Search results
  searchThemes: (query: string) => `search:themes:${query}`,
  searchFonts: (query: string) => `search:fonts:${query}`,
  searchUsers: (query: string) => `search:users:${query}`,
  
  // Public profiles
  publicProfile: (username: string) => `public:profile:${username}`,
  publicLinks: (username: string) => `public:links:${username}`,
};

// TTL constants (in milliseconds)
export const CacheTTL = {
  SHORT: 1 * 60 * 1000,      // 1 minute
  MEDIUM: 5 * 60 * 1000,     // 5 minutes  
  LONG: 30 * 60 * 1000,      // 30 minutes
  VERY_LONG: 2 * 60 * 60 * 1000, // 2 hours
  USER_DATA: 10 * 60 * 1000, // 10 minutes
  STATIC_DATA: 60 * 60 * 1000, // 1 hour
};

// Auto cleanup interval
if (typeof window !== 'undefined') {
  setInterval(() => {
    useCacheStore.getState().cleanup();
  }, DEFAULT_CONFIG.cleanupInterval);
}
