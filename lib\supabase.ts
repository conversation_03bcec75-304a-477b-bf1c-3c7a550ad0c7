import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create Supabase client (legacy - for backward compatibility)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Create SSR-compatible browser client for proper cookie handling
export const createSupabaseBrowserClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true // This ensures OAuth callbacks are handled automatically
    }
  })
}

// Database table names
export const TABLES = {
  PROFILES: 'profiles', // User profiles (separate from auth.users)
  LINKS: 'links',
  ANALYTICS: 'analytics',
  THEMES: 'themes',
  FONTS: 'fonts',
  LINK_CARD_STYLES: 'link_card_styles'
} as const

// Storage bucket names
export const BUCKETS = {
  AVATARS: 'avatars',
  BACKGROUNDS: 'backgrounds'
} as const

// Helper function to get authenticated user (using SSR client)
export const getCurrentUser = async () => {
  const supabase = createSupabaseBrowserClient()
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

// Helper function to get session (using SSR client)
export const getSession = async () => {
  const supabase = createSupabaseBrowserClient()
  const { data: { session }, error } = await supabase.auth.getSession()
  if (error) throw error
  return session
}

export default supabase
