/**
 * Image utilities for handling file uploads and previews without localStorage
 */

/**
 * Create a temporary URL for immediate image preview
 * This doesn't store anything in localStorage - just creates a blob URL
 */
export function createImagePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Clean up temporary blob URLs to prevent memory leaks
 */
export function cleanupImagePreview(url: string): void {
  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
}

/**
 * Quick synchronous validation for basic file checks
 */
export function validateImageFileSync(file: File): { valid: boolean; error?: string } {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'Please select an image file' };
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { valid: false, error: 'Image must be smaller than 10MB' };
  }

  return { valid: true };
}

/**
 * Validate image file before creating preview
 */
export function validateImageFile(file: File): Promise<{ valid: boolean; error?: string }> {
  return new Promise((resolve) => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      resolve({ valid: false, error: 'Please select an image file' });
      return;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      resolve({ valid: false, error: 'Image must be smaller than 10MB' });
      return;
    }

    // Check if it's actually a valid image by trying to load it
    const img = new Image();
    img.onload = () => {
      // Clean up the object URL
      URL.revokeObjectURL(img.src);
      resolve({ valid: true });
    };
    img.onerror = () => {
      // Clean up the object URL
      URL.revokeObjectURL(img.src);
      resolve({ valid: false, error: 'Invalid image file' });
    };
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Handle image file selection with validation and preview
 * This creates a temporary preview URL for immediate display
 * The actual upload to storage should be done separately when saving
 */
export async function handleImageUpload(
  file: File,
  onSuccess: (previewUrl: string, file: File) => void,
  onError: (error: string) => void
): Promise<void> {
  try {
    // First do quick synchronous validation
    const syncValidation = validateImageFileSync(file);
    if (!syncValidation.valid) {
      onError(syncValidation.error || 'Invalid image file');
      return;
    }

    // Then do full async validation (loads the image)
    const validation = await validateImageFile(file);
    if (!validation.valid) {
      onError(validation.error || 'Invalid image file');
      return;
    }

    // Create temporary preview URL for immediate display
    const previewUrl = createImagePreview(file);
    onSuccess(previewUrl, file);
  } catch (error) {
    onError('Failed to create image preview');
  }
}

/**
 * Handle image upload to Supabase Storage
 * This uploads the file and returns the permanent URL
 */
export async function handleImageUploadToStorage(
  file: File,
  userId: string,
  type: 'avatar' | 'background',
  onSuccess: (permanentUrl: string, fileId: string) => void,
  onError: (error: string) => void
): Promise<void> {
  try {
    const { fileUploadService } = await import('./file-upload-service');

    // Validate file first
    const validation = fileUploadService.validateImageFile(file);
    if (!validation.valid) {
      onError(validation.error || 'Invalid image file');
      return;
    }

    // Upload to appropriate bucket
    let result;
    if (type === 'avatar') {
      result = await fileUploadService.uploadAvatar(file, userId);
    } else {
      result = await fileUploadService.uploadBackground(file, userId);
    }

    onSuccess(result.url, result.fileId);
  } catch (error) {
    console.error('Upload error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';
    onError(errorMessage);
  }
}

/**
 * Compress image file for better performance (optional)
 */
export function compressImage(
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 600,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Convert File to base64 (only when needed for upload)
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * Get image dimensions
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
      URL.revokeObjectURL(img.src);
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Create a safe image preview component props
 */
export function createImagePreviewProps(file: File) {
  const previewUrl = createImagePreview(file);
  
  return {
    src: previewUrl,
    onLoad: () => {
      // Image loaded successfully
    },
    onError: () => {
      // Clean up on error
      cleanupImagePreview(previewUrl);
    }
  };
}

/**
 * Hook for managing image previews with cleanup
 */
export function useImagePreview() {
  const previews = new Set<string>();

  const createPreview = (file: File): string => {
    const url = createImagePreview(file);
    previews.add(url);
    return url;
  };

  const cleanupPreview = (url: string): void => {
    if (previews.has(url)) {
      cleanupImagePreview(url);
      previews.delete(url);
    }
  };

  const cleanupAll = (): void => {
    previews.forEach(url => cleanupImagePreview(url));
    previews.clear();
  };

  return {
    createPreview,
    cleanupPreview,
    cleanupAll
  };
}
