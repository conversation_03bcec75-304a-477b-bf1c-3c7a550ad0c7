import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { userService } from '../user-service';
import { dataSyncService } from '../data-sync-service';

export interface Link {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  customStyle?: string;
  clicks: number;
  isActive: boolean;
  order: number;
}

export interface SocialLink {
  platform: string;
  url: string;
  isVisible: boolean;
}

export interface UserProfile {
  id: string;
  name: string;
  username: string;
  bio: string;
  avatar: string;
  avatarFile?: File; // Temporary file for upload, not persisted
  email: string;
  template: 'minimal' | 'dark' | 'neon' | 'pastel';
  customColors?: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
  // Enhanced styling options
  backgroundImage?: string;
  backgroundImageFile?: File; // Temporary file for upload, not persisted
  backgroundColor?: string;
  backgroundType?: 'color' | 'image' | 'gradient' | 'none';
  gradientColors?: string[];
  fontFamily?: string;
  fontSize?: 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large' | 'full';
  glassmorphism?: boolean;
  customCSS?: string;
  socialLinksStyle?: {
    position: 'top' | 'bottom' | 'sidebar';
    style: 'icons' | 'buttons' | 'minimal';
    size: 'small' | 'medium' | 'large';
  };
  defaultLinkStyle?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    shadow?: boolean;
    hoverEffect?: 'none' | 'lift' | 'glow' | 'scale';
  };
}

export interface Analytics {
  totalViews: number;
  totalClicks: number;
  linkClicks: Record<string, number>;
  dailyViews: Record<string, number>;
  referrers: Record<string, number>;
}

interface UserState {
  // User Profile
  profile: UserProfile | null;
  
  // Links
  links: Link[];
  
  // Social Links
  socialLinks: SocialLink[];
  
  // Analytics
  analytics: Analytics;
  
  // UI State
  isLoading: boolean;
  isLinksLoading: boolean;
  activeTab: string;
  
  // Actions
  setProfile: (profile: UserProfile) => void;
  updateProfile: (updates: Partial<UserProfile>) => void;
  syncFromAuthProfile: (authProfile: any) => void;
  
  // Link Actions
  addLink: (link: Omit<Link, 'id' | 'clicks' | 'order'>) => void;
  updateLink: (id: string, updates: Partial<Link>) => void;
  deleteLink: (id: string) => void;
  reorderLinks: (links: Link[]) => void;
  
  // Social Link Actions
  updateSocialLink: (platform: string, url: string, isVisible: boolean) => void;
  
  // Analytics Actions
  incrementLinkClick: (linkId: string) => void;
  incrementPageView: () => void;
  
  // UI Actions
  setActiveTab: (tab: string) => void;
  setLoading: (loading: boolean) => void;
  setLinksLoading: (loading: boolean) => void;

  // Data Sync Actions
  saveToAppwrite: () => Promise<boolean>;
  loadFromAppwrite: () => Promise<boolean>;
  autoSave: () => void;

  // Reset
  reset: () => void;
}

const initialState = {
  profile: null, // No default profile - will be loaded from Appwrite or created by user
  links: [], // No default links - user starts with empty state
  socialLinks: [], // No default social links
  analytics: {
    totalViews: 0,
    totalClicks: 0,
    linkClicks: {},
    dailyViews: {},
    referrers: {},
  },
  isLoading: false,
  isLinksLoading: false,
  activeTab: 'builder',
};

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setProfile: (profile) => set({ profile }),
      
      updateProfile: (updates) => set((state) => {
        // If no profile exists, create a new one with the updates
        if (!state.profile) {
          const { user } = require('../stores/auth-store').useAuthStore.getState();
          return {
            profile: {
              id: user?.$id || '',
              name: user?.name || '',
              username: user?.email?.split('@')[0] || '',
              bio: '',
              avatar: '',
              email: user?.email || '',
              template: 'minimal' as const,
              ...updates
            }
          };
        }
        // If profile exists, update it
        return {
          profile: { ...state.profile, ...updates }
        };
      }),

      syncFromAuthProfile: (authProfile) => {
        if (authProfile) {
          set({
            profile: {
              id: authProfile.$id || authProfile.userId,
              name: authProfile.name,
              username: authProfile.username,
              email: authProfile.email,
              bio: authProfile.bio || '',
              avatar: authProfile.avatar || '',
              template: authProfile.template || 'minimal',
              customColors: authProfile.customColors,
            }
          });
        }
      },
      
      addLink: (linkData) => set((state) => {
        const newLink: Link = {
          ...linkData,
          id: Date.now().toString(),
          clicks: 0,
          isActive: true,
          order: state.links.length,
        };
        return { links: [...state.links, newLink] };
      }),
      
      updateLink: (id, updates) => set((state) => ({
        links: state.links.map((link) =>
          link.id === id ? { ...link, ...updates } : link
        ),
      })),
      
      deleteLink: (id) => set((state) => ({
        links: state.links.filter((link) => link.id !== id),
      })),
      
      reorderLinks: (links) => set({ links }),
      
      updateSocialLink: (platform, url, isVisible) => set((state) => ({
        socialLinks: state.socialLinks.map((social) =>
          social.platform === platform
            ? { ...social, url, isVisible }
            : social
        ),
      })),
      
      incrementLinkClick: (linkId) => set((state) => ({
        links: state.links.map((link) =>
          link.id === linkId ? { ...link, clicks: link.clicks + 1 } : link
        ),
        analytics: {
          ...state.analytics,
          totalClicks: state.analytics.totalClicks + 1,
          linkClicks: {
            ...state.analytics.linkClicks,
            [linkId]: (state.analytics.linkClicks[linkId] || 0) + 1,
          },
        },
      })),
      
      incrementPageView: () => set((state) => ({
        analytics: {
          ...state.analytics,
          totalViews: state.analytics.totalViews + 1,
        },
      })),
      
      setActiveTab: (tab) => set({ activeTab: tab }),

      setLoading: (loading) => set({ isLoading: loading }),
      setLinksLoading: (loading) => set({ isLinksLoading: loading }),

      // Data Sync Actions
      saveToAppwrite: async () => {
        try {
          const state = get();
          const { user } = require('../stores/auth-store').useAuthStore.getState();

          if (!user || !state.profile) {
            throw new Error('User not authenticated or profile not found');
          }

          set({ isLoading: true });
          await dataSyncService.saveAllUserData(user.id, state.profile, state.links);
          set({ isLoading: false });
          return true;
        } catch (error) {
          console.error('Failed to save to Appwrite:', error);
          set({ isLoading: false });
          return false;
        }
      },

      loadFromAppwrite: async () => {
        try {
          const { user } = require('../stores/auth-store').useAuthStore.getState();
          if (!user) {
            throw new Error('User not authenticated');
          }

          set({ isLoading: true });
          const { setProfile, reorderLinks } = get();
          await dataSyncService.syncStoreWithAppwrite(user.id, setProfile, reorderLinks);
          set({ isLoading: false });
          return true;
        } catch (error) {
          console.error('Failed to load from Appwrite:', error);
          set({ isLoading: false });
          return false;
        }
      },

      autoSave: () => {
        const state = get();
        const { user } = require('../stores/auth-store').useAuthStore.getState();

        if (user && state.profile) {
          dataSyncService.autoSave(user.id, state.profile, state.links);
        }
      },

      reset: () => set(initialState),
    }),
    {
      name: 'linkvibe-user-storage',
      partialize: (state) => ({
        profile: state.profile ? {
          ...state.profile,
          // Don't persist large data URLs or temporary files
          avatar: state.profile.avatar?.startsWith('data:') || state.profile.avatar?.startsWith('blob:') ? '' : state.profile.avatar,
          avatarFile: undefined, // Never persist File objects
          backgroundImage: state.profile.backgroundImage?.startsWith('data:') || state.profile.backgroundImage?.startsWith('blob:') ? '' : state.profile.backgroundImage,
          backgroundImageFile: undefined, // Never persist File objects
        } : null,
        links: state.links,
        socialLinks: state.socialLinks,
        // Don't persist analytics to save space - this can be fetched from server
        // analytics: state.analytics,
      }),
      // Use SSR-safe storage
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);
