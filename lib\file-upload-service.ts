import { supabase } from './supabase';
import { BUCKETS } from './supabase';

export interface UploadResult {
  fileId: string;
  url: string;
  fileName: string;
  size: number;
}

export class FileUploadService {
  /**
   * Upload avatar image to Supabase Storage
   */
  async uploadAvatar(file: File, userId: string): Promise<UploadResult> {
    try {
      console.log('📤 Uploading avatar to Supabase Storage...');

      // Create a unique filename
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const fileName = `${userId}/avatar_${Date.now()}.${fileExtension}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(BUCKETS.AVATARS)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from(BUCKETS.AVATARS)
        .getPublicUrl(fileName);

      console.log('✅ Avatar uploaded successfully:', data.path);

      return {
        fileId: data.path,
        url: publicUrl,
        fileName: fileName,
        size: file.size
      };
    } catch (error) {
      console.error('❌ Failed to upload avatar:', error);
      throw new Error('Failed to upload avatar image');
    }
  }

  /**
   * Upload background image to Supabase Storage
   */
  async uploadBackground(file: File, userId: string): Promise<UploadResult> {
    try {
      console.log('📤 Uploading background to Supabase Storage...');
      console.log('📋 File details:', { name: file.name, size: file.size, type: file.type });
      console.log('👤 User ID:', userId);

      // Create a unique filename
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const fileName = `${userId}/background_${Date.now()}.${fileExtension}`;
      console.log('📁 File name:', fileName);

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(BUCKETS.BACKGROUNDS)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error('❌ Supabase storage error:', error);
        console.error('❌ Error details:', JSON.stringify(error, null, 2));
        throw error;
      }

      if (!data) {
        console.error('❌ No data returned from upload');
        throw new Error('No data returned from upload');
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from(BUCKETS.BACKGROUNDS)
        .getPublicUrl(fileName);

      console.log('✅ Background uploaded successfully:', data.path);
      console.log('🔗 Public URL:', publicUrl);

      return {
        fileId: data.path,
        url: publicUrl,
        fileName: fileName,
        size: file.size
      };
    } catch (error) {
      console.error('❌ Failed to upload background:', error);
      console.error('❌ Error type:', typeof error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));
      throw new Error('Failed to upload background image');
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(bucketName: string, filePath: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from(bucketName)
        .remove([filePath]);

      if (error) throw error;
      console.log('🗑️ File deleted successfully:', filePath);
    } catch (error) {
      console.error('❌ Failed to delete file:', error);
      // Don't throw error for deletion failures - it's not critical
    }
  }

  /**
   * Delete avatar file
   */
  async deleteAvatar(filePath: string): Promise<void> {
    return this.deleteFile(BUCKETS.AVATARS, filePath);
  }

  /**
   * Delete background file
   */
  async deleteBackground(filePath: string): Promise<void> {
    return this.deleteFile(BUCKETS.BACKGROUNDS, filePath);
  }

  /**
   * Get file info
   */
  async getFileInfo(bucketName: string, filePath: string) {
    try {
      const { data, error } = await supabase.storage
        .from(bucketName)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          search: filePath.split('/').pop()
        });

      if (error) throw error;
      return data?.[0] || null;
    } catch (error) {
      console.error('❌ Failed to get file info:', error);
      return null;
    }
  }

  /**
   * Validate image file before upload
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: 'Please select an image file' };
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { valid: false, error: 'Image must be smaller than 10MB' };
    }

    // Check for common image formats
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Please use JPEG, PNG, GIF, or WebP format' };
    }

    return { valid: true };
  }

  /**
   * Compress image before upload (optional)
   */
  async compressImage(
    file: File,
    maxWidth: number = 800,
    maxHeight: number = 600,
    quality: number = 0.8
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }
}

// Export singleton instance
export const fileUploadService = new FileUploadService();
