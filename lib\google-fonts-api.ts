interface GoogleFontVariant {
  regular?: string;
  italic?: string;
  '100'?: string;
  '100italic'?: string;
  '200'?: string;
  '200italic'?: string;
  '300'?: string;
  '300italic'?: string;
  '400'?: string;
  '400italic'?: string;
  '500'?: string;
  '500italic'?: string;
  '600'?: string;
  '600italic'?: string;
  '700'?: string;
  '700italic'?: string;
  '800'?: string;
  '800italic'?: string;
  '900'?: string;
  '900italic'?: string;
}

export interface GoogleFont {
  family: string;
  variants: string[];
  subsets: string[];
  version: string;
  lastModified: string;
  files: GoogleFontVariant;
  category: 'serif' | 'sans-serif' | 'monospace' | 'cursive' | 'fantasy';
  kind: string;
  menu?: string;
}

export interface GoogleFontsResponse {
  kind: string;
  items: GoogleFont[];
}

export class GoogleFontsAPI {
  private apiKey: string;
  private baseUrl = 'https://www.googleapis.com/webfonts/v1/webfonts';
  private cache: Map<string, GoogleFontsResponse> = new Map();
  private cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_GOOGLE_FONTS_API_KEY || '';
    if (!this.apiKey) {
      console.warn('Google Fonts API key not found. Font fetching will be limited.');
    }
  }

  /**
   * Fetch all Google Fonts
   */
  async getAllFonts(sort: 'alpha' | 'date' | 'popularity' | 'style' | 'trending' = 'popularity'): Promise<GoogleFont[]> {
    if (!this.apiKey) {
      console.warn('Google Fonts API key not configured');
      return [];
    }

    const cacheKey = `all-fonts-${sort}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && this.isCacheValid(cached)) {
      return cached.items;
    }

    try {
      const url = `${this.baseUrl}?key=${this.apiKey}&sort=${sort}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Google Fonts API error: ${response.status} ${response.statusText}`);
      }

      const data: GoogleFontsResponse = await response.json();
      
      // Cache the response
      this.cache.set(cacheKey, { ...data, timestamp: Date.now() } as any);
      
      return data.items;
    } catch (error) {
      console.error('Failed to fetch Google Fonts:', error);
      return [];
    }
  }

  /**
   * Fetch fonts by category
   */
  async getFontsByCategory(category: string, sort: 'alpha' | 'date' | 'popularity' | 'style' | 'trending' = 'popularity'): Promise<GoogleFont[]> {
    const allFonts = await this.getAllFonts(sort);
    return allFonts.filter(font => font.category === category);
  }

  /**
   * Search fonts by name
   */
  async searchFonts(query: string, sort: 'alpha' | 'date' | 'popularity' | 'style' | 'trending' = 'popularity'): Promise<GoogleFont[]> {
    const allFonts = await this.getAllFonts(sort);
    const searchTerm = query.toLowerCase();
    
    return allFonts.filter(font => 
      font.family.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get popular fonts (first 50 by popularity)
   */
  async getPopularFonts(limit: number = 50): Promise<GoogleFont[]> {
    const fonts = await this.getAllFonts('popularity');
    return fonts.slice(0, limit);
  }

  /**
   * Get trending fonts
   */
  async getTrendingFonts(limit: number = 20): Promise<GoogleFont[]> {
    const fonts = await this.getAllFonts('trending');
    return fonts.slice(0, limit);
  }

  /**
   * Get font by family name
   */
  async getFontByFamily(family: string): Promise<GoogleFont | null> {
    const allFonts = await this.getAllFonts();
    return allFonts.find(font => font.family === family) || null;
  }

  /**
   * Generate CSS import URL for a font
   */
  generateFontImportUrl(family: string, variants: string[] = ['400', '700']): string {
    const encodedFamily = family.replace(/\s+/g, '+');
    const variantString = variants.join(',');
    return `https://fonts.googleapis.com/css2?family=${encodedFamily}:wght@${variantString}&display=swap`;
  }

  /**
   * Load a Google Font dynamically in the browser
   */
  loadFont(family: string, variants: string[] = ['400', '700']): void {
    if (typeof window === 'undefined') return;

    const fontId = `google-font-${family.replace(/\s+/g, '-').toLowerCase()}`;
    
    // Check if font is already loaded
    if (document.getElementById(fontId)) {
      return;
    }

    const importUrl = this.generateFontImportUrl(family, variants);

    // Create and append link element
    const link = document.createElement('link');
    link.id = fontId;
    link.rel = 'stylesheet';
    link.href = importUrl;
    document.head.appendChild(link);
  }

  /**
   * Convert Google Font to our internal Font format
   */
  convertToInternalFormat(googleFont: GoogleFont): any {
    return {
      name: googleFont.family,
      display_name: googleFont.family,
      category: googleFont.category,
      variants: this.parseVariants(googleFont.variants),
      subsets: googleFont.subsets,
      tags: this.generateTags(googleFont),
      css_import_url: this.generateFontImportUrl(googleFont.family),
      css_font_family: `"${googleFont.family}", ${googleFont.category === 'serif' ? 'serif' : googleFont.category === 'monospace' ? 'monospace' : googleFont.category === 'cursive' ? 'cursive' : 'sans-serif'}`,
      is_google_font: true,
      is_system_font: false,
      is_premium: false,
      google_font_data: googleFont
    };
  }

  /**
   * Parse Google Font variants into our format
   */
  private parseVariants(variants: string[]): Array<{ weight: string; style: string }> {
    return variants.map(variant => {
      if (variant === 'regular') {
        return { weight: '400', style: 'normal' };
      } else if (variant === 'italic') {
        return { weight: '400', style: 'italic' };
      } else if (variant.endsWith('italic')) {
        return { weight: variant.replace('italic', ''), style: 'italic' };
      } else {
        return { weight: variant, style: 'normal' };
      }
    });
  }

  /**
   * Generate tags based on font properties
   */
  private generateTags(font: GoogleFont): string[] {
    const tags = new Set(['google-fonts']); // Use Set to prevent duplicates

    // Add category tag
    tags.add(font.category);

    // Add popularity tags based on position (this is approximate)
    if (font.family.includes('Sans') || font.family.includes('Roboto') || font.family.includes('Open')) {
      tags.add('popular');
    }

    // Add style tags based on name
    if (font.family.toLowerCase().includes('script') || font.family.toLowerCase().includes('handwriting')) {
      tags.add('handwriting');
      tags.add('script');
    }

    if (font.family.toLowerCase().includes('display') || font.family.toLowerCase().includes('title')) {
      tags.add('display');
      tags.add('headers');
    }

    if (font.family.toLowerCase().includes('mono') || font.family.toLowerCase().includes('code')) {
      tags.add('code');
      tags.add('technical');
    }

    return Array.from(tags);
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(cached: any): boolean {
    return cached.timestamp && (Date.now() - cached.timestamp) < this.cacheExpiry;
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const googleFontsAPI = new GoogleFontsAPI();
