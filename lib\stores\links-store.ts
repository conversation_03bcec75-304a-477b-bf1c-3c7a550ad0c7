import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { cachedAPI } from '../cached-api';
import { LinkData } from '../links-service';

interface LinksState {
  // Data
  links: LinkData[];
  isLoading: boolean;
  isSaving: boolean;
  lastFetch: number | null;
  userId: string | null;
  
  // Actions
  setUserId: (userId: string | null) => void;
  loadLinks: (userId: string, forceRefresh?: boolean) => Promise<void>;
  addLink: (userId: string, linkData: Omit<LinkData, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'clicks'>) => Promise<LinkData>;
  addTempLink: (linkData: LinkData) => void; // Add temporary link to local state only
  updateLink: (linkId: string, updates: Partial<LinkData>) => Promise<LinkData>;
  updateTempLink: (linkId: string, updates: Partial<LinkData>) => void; // Update temporary link in local state only
  deleteLink: (linkId: string) => Promise<void>;
  deleteTempLink: (linkId: string) => void; // Delete temporary link from local state only
  reorderLinks: (newOrder: LinkData[]) => void;
  incrementLinkClick: (linkId: string) => Promise<void>;
  toggleLinkActive: (linkId: string) => Promise<void>;
  duplicateLink: (linkId: string) => Promise<LinkData>;
  
  // Bulk operations
  saveAllLinks: (userId: string) => Promise<void>;
  clearLinks: () => void;
  
  // Getters
  getActiveLinks: () => LinkData[];
  getLinkById: (linkId: string) => LinkData | undefined;
  getLinksByOrder: () => LinkData[];
}

export const useLinksStore = create<LinksState>()(
  persist(
    (set, get) => ({
      // Initial state
      links: [],
      isLoading: false,
      isSaving: false,
      lastFetch: null,
      userId: null,

      setUserId: (userId) => {
        const currentUserId = get().userId;
        if (currentUserId !== userId) {
          // Clear links when user changes
          set({ 
            userId, 
            links: [], 
            lastFetch: null 
          });
        }
      },

      loadLinks: async (userId: string, forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

        // Skip loading if recently fetched (unless forced)
        if (!forceRefresh && state.lastFetch && 
            state.userId === userId &&
            (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Links: Using cached data');
          return;
        }

        set({ isLoading: true });
        try {
          console.log('🔄 Loading links for user:', userId);
          const rawLinks = await cachedAPI.getUserLinks(userId, forceRefresh);

          // Convert database fields (snake_case) to frontend fields (camelCase)
          const links = rawLinks?.map(link => ({
            id: link.id,
            title: link.title,
            url: link.url,
            description: link.description || '',
            icon: link.icon || '',
            backgroundColor: link.background_color || '',
            textColor: link.text_color || '',
            borderColor: link.border_color || '',
            customStyle: link.custom_style || '',
            clicks: link.clicks || 0,
            isActive: link.is_active,
            order: link.order,
            user_id: link.user_id,
            created_at: link.created_at,
            updated_at: link.updated_at
          })) || [];

          set({
            links,
            isLoading: false,
            lastFetch: now,
            userId
          });

          console.log('✅ Links loaded:', links?.length || 0);
        } catch (error) {
          console.error('❌ Failed to load links:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      addLink: async (userId: string, linkData) => {
        set({ isSaving: true });
        try {
          console.log('➕ Adding new link');
          const rawNewLink = await cachedAPI.createLink(userId, linkData);

          // Convert database fields to frontend fields
          const newLink = {
            id: rawNewLink.id,
            title: rawNewLink.title,
            url: rawNewLink.url,
            description: rawNewLink.description || '',
            icon: rawNewLink.icon || '',
            backgroundColor: rawNewLink.background_color || '',
            textColor: rawNewLink.text_color || '',
            borderColor: rawNewLink.border_color || '',
            customStyle: rawNewLink.custom_style || '',
            clicks: rawNewLink.clicks || 0,
            isActive: rawNewLink.is_active,
            order: rawNewLink.order,
            user_id: rawNewLink.user_id,
            created_at: rawNewLink.created_at,
            updated_at: rawNewLink.updated_at
          };

          set((state) => ({
            links: [...state.links, newLink],
            isSaving: false
          }));

          console.log('✅ Link added:', newLink.id);
          return newLink;
        } catch (error) {
          console.error('❌ Failed to add link:', error);
          set({ isSaving: false });
          throw error;
        }
      },

      addTempLink: (linkData) => {
        set(state => ({
          links: [...state.links, linkData],
        }));
      },

      updateTempLink: (linkId: string, updates) => {
        set(state => ({
          links: state.links.map(link =>
            link.id === linkId ? { ...link, ...updates } : link
          ),
        }));
      },

      deleteTempLink: (linkId) => {
        set(state => ({
          links: state.links.filter(link => link.id !== linkId),
        }));
      },

      updateLink: async (linkId: string, updates) => {
        set({ isSaving: true });
        try {
          console.log('📝 Updating link:', linkId);

          // Convert frontend fields to database fields for the update
          const dbUpdates = { ...updates };
          if (updates.backgroundColor !== undefined) {
            dbUpdates.background_color = updates.backgroundColor;
            delete dbUpdates.backgroundColor;
          }
          if (updates.textColor !== undefined) {
            dbUpdates.text_color = updates.textColor;
            delete dbUpdates.textColor;
          }
          if (updates.borderColor !== undefined) {
            dbUpdates.border_color = updates.borderColor;
            delete dbUpdates.borderColor;
          }
          if (updates.customStyle !== undefined) {
            dbUpdates.custom_style = updates.customStyle;
            delete dbUpdates.customStyle;
          }
          if (updates.isActive !== undefined) {
            dbUpdates.is_active = updates.isActive;
            delete dbUpdates.isActive;
          }

          const rawUpdatedLink = await cachedAPI.updateLink(linkId, dbUpdates);

          // Convert database fields back to frontend fields
          const updatedLink = {
            id: rawUpdatedLink.id,
            title: rawUpdatedLink.title,
            url: rawUpdatedLink.url,
            description: rawUpdatedLink.description || '',
            icon: rawUpdatedLink.icon || '',
            backgroundColor: rawUpdatedLink.background_color || '',
            textColor: rawUpdatedLink.text_color || '',
            borderColor: rawUpdatedLink.border_color || '',
            customStyle: rawUpdatedLink.custom_style || '',
            clicks: rawUpdatedLink.clicks || 0,
            isActive: rawUpdatedLink.is_active,
            order: rawUpdatedLink.order,
            user_id: rawUpdatedLink.user_id,
            created_at: rawUpdatedLink.created_at,
            updated_at: rawUpdatedLink.updated_at
          };

          set((state) => ({
            links: state.links.map(link =>
              link.id === linkId ? updatedLink : link
            ),
            isSaving: false
          }));

          console.log('✅ Link updated:', linkId);
          return updatedLink;
        } catch (error) {
          console.error('❌ Failed to update link:', error);
          set({ isSaving: false });
          throw error;
        }
      },

      deleteLink: async (linkId: string) => {
        set({ isSaving: true });
        try {
          console.log('🗑️ Deleting link:', linkId);
          await cachedAPI.deleteLink(linkId);
          
          set((state) => ({
            links: state.links.filter(link => link.id !== linkId),
            isSaving: false
          }));
          
          console.log('✅ Link deleted:', linkId);
        } catch (error) {
          console.error('❌ Failed to delete link:', error);
          set({ isSaving: false });
          throw error;
        }
      },

      reorderLinks: (newOrder) => {
        console.log('🔄 Reordering links');
        set({ links: newOrder });
        
        // Update order values
        const updatedLinks = newOrder.map((link, index) => ({
          ...link,
          order: index
        }));
        
        set({ links: updatedLinks });
      },

      incrementLinkClick: async (linkId: string) => {
        try {
          // Optimistically update the UI
          set((state) => ({
            links: state.links.map(link =>
              link.id === linkId 
                ? { ...link, clicks: (link.clicks || 0) + 1 }
                : link
            )
          }));

          // Update in background (don't await to avoid blocking UI)
          cachedAPI.updateLink(linkId, { 
            clicks: get().links.find(l => l.id === linkId)?.clicks || 0 
          }).catch(error => {
            console.error('❌ Failed to update click count:', error);
            // Revert optimistic update on error
            set((state) => ({
              links: state.links.map(link =>
                link.id === linkId 
                  ? { ...link, clicks: Math.max(0, (link.clicks || 1) - 1) }
                  : link
              )
            }));
          });
        } catch (error) {
          console.error('❌ Failed to increment link click:', error);
        }
      },

      toggleLinkActive: async (linkId: string) => {
        const link = get().links.find(l => l.id === linkId);
        if (!link) return;

        try {
          await get().updateLink(linkId, { isActive: !link.isActive });
        } catch (error) {
          console.error('❌ Failed to toggle link active state:', error);
          throw error;
        }
      },

      duplicateLink: async (linkId: string) => {
        const state = get();
        const originalLink = state.links.find(l => l.id === linkId);
        if (!originalLink || !state.userId) {
          throw new Error('Link not found or user not set');
        }

        const duplicateData = {
          title: `${originalLink.title} (Copy)`,
          url: originalLink.url,
          description: originalLink.description,
          icon: originalLink.icon,
          background_color: originalLink.background_color,
          text_color: originalLink.text_color,
          border_color: originalLink.border_color,
          custom_style: originalLink.custom_style,
          is_active: false, // Start as inactive
          order: state.links.length
        };

        return get().addLink(state.userId, duplicateData);
      },

      saveAllLinks: async (userId: string) => {
        const state = get();
        set({ isSaving: true });

        try {
          console.log('💾 Saving all links');

          // Separate temporary links from existing links
          const tempLinks = state.links.filter(link => link.id.startsWith('temp-'));
          const existingLinks = state.links.filter(link => !link.id.startsWith('temp-'));

          // Create new links for temporary ones
          const newLinksPromises = tempLinks.map(async (tempLink, index) => {
            const linkData = {
              title: tempLink.title,
              url: tempLink.url,
              description: tempLink.description,
              icon: tempLink.icon,
              background_color: tempLink.backgroundColor,
              text_color: tempLink.textColor,
              border_color: tempLink.borderColor,
              custom_style: tempLink.customStyle,
              is_active: tempLink.isActive,
              order: existingLinks.length + index, // Place after existing links
            };

            console.log('➕ Creating new link:', tempLink.title);
            return await cachedAPI.createLink(userId, linkData);
          });

          // Wait for all new links to be created
          const newLinks = await Promise.all(newLinksPromises);

          // Update order for existing links
          const updatePromises = existingLinks.map((link, index) =>
            get().updateLink(link.id!, { order: index })
          );

          await Promise.all(updatePromises);

          // Update state with new links (replace temp links with real ones)
          const updatedLinks = [
            ...existingLinks,
            ...newLinks.map(rawLink => ({
              id: rawLink.id,
              title: rawLink.title,
              url: rawLink.url,
              description: rawLink.description || '',
              icon: rawLink.icon || '',
              backgroundColor: rawLink.background_color || '',
              textColor: rawLink.text_color || '',
              borderColor: rawLink.border_color || '',
              customStyle: rawLink.custom_style || '',
              clicks: rawLink.clicks || 0,
              isActive: rawLink.is_active,
              order: rawLink.order,
              user_id: rawLink.user_id,
              created_at: rawLink.created_at,
              updated_at: rawLink.updated_at
            }))
          ].sort((a, b) => (a.order || 0) - (b.order || 0));

          set({
            links: updatedLinks,
            isSaving: false
          });

          console.log('✅ All links saved');
        } catch (error) {
          console.error('❌ Failed to save all links:', error);
          set({ isSaving: false });
          throw error;
        }
      },

      clearLinks: () => {
        set({ 
          links: [], 
          lastFetch: null,
          userId: null 
        });
      },

      // Getters
      getActiveLinks: () => {
        return get().links
          .filter(link => link.isActive)
          .sort((a, b) => (a.order || 0) - (b.order || 0));
      },

      getLinkById: (linkId: string) => {
        return get().links.find(link => link.id === linkId);
      },

      getLinksByOrder: () => {
        return get().links.sort((a, b) => (a.order || 0) - (b.order || 0));
      },
    }),
    {
      name: 'linkvibe-links-storage',
      partialize: (state) => ({
        links: state.links,
        userId: state.userId,
        lastFetch: state.lastFetch,
        // Don't persist loading states
      }),
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);
