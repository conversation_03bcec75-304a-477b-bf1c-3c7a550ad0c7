import { supabase } from './supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

export interface User {
  id: string;
  name: string;
  email: string;
  emailVerification: boolean;
  prefs: Record<string, any>;
}

export class AuthService {
  // Use regular supabase client for better OAuth compatibility

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;

      if (!user) return null;

      return {
        id: user.id,
        name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
        email: user.email || '',
        emailVerification: user.email_confirmed_at !== null,
        prefs: user.user_metadata || {}
      };
    } catch (error) {
      console.log('No user session found');
      return null;
    }
  }

  // Create account with email and password
  async createAccount(email: string, password: string, name: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name
          }
        }
      });

      if (error) throw error;
      if (!data.user) throw new Error('Failed to create account');

      return {
        id: data.user.id,
        name: name,
        email: email,
        emailVerification: data.user.email_confirmed_at !== null,
        prefs: data.user.user_metadata || {}
      };
    } catch (error) {
      console.error('Account creation failed:', error);
      throw error;
    }
  }

  // Login with email and password
  async login(email: string, password: string): Promise<any> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
      return data.session;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  // Login with Google OAuth
  async loginWithGoogle(): Promise<void> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent select_account', // Force consent screen and account selection
            include_granted_scopes: 'true'
          }
        }
      });

      if (error) throw error;
    } catch (error) {
      console.error('Google login failed:', error);
      throw error;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }

  // Send password recovery email
  async sendPasswordRecovery(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });
      if (error) throw error;
    } catch (error) {
      console.error('Password recovery failed:', error);
      throw error;
    }
  }

  // Complete password recovery
  async completePasswordRecovery(
    accessToken: string,
    refreshToken: string,
    password: string
  ): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });
      if (error) throw error;
    } catch (error) {
      console.error('Password reset failed:', error);
      throw error;
    }
  }

  // Send email verification
  async sendEmailVerification(): Promise<void> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: '', // Will use current user's email
        options: {
          emailRedirectTo: `${window.location.origin}/auth/verify`
        }
      });
      if (error) throw error;
    } catch (error) {
      console.error('Email verification failed:', error);
      throw error;
    }
  }

  // Verify email (handled automatically by Supabase)
  async verifyEmail(token: string, type: string): Promise<void> {
    try {
      const { error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: type as any
      });
      if (error) throw error;
    } catch (error) {
      console.error('Email verification failed:', error);
      throw error;
    }
  }

  // Update user preferences
  async updatePreferences(prefs: Record<string, any>): Promise<User> {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: prefs
      });
      if (error) throw error;
      if (!data.user) throw new Error('Failed to update preferences');

      return {
        id: data.user.id,
        name: data.user.user_metadata?.name || data.user.email?.split('@')[0] || 'User',
        email: data.user.email || '',
        emailVerification: data.user.email_confirmed_at !== null,
        prefs: data.user.user_metadata || {}
      };
    } catch (error) {
      console.error('Update preferences failed:', error);
      throw error;
    }
  }

  // Update user name
  async updateName(name: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: { name }
      });
      if (error) throw error;
      if (!data.user) throw new Error('Failed to update name');

      return {
        id: data.user.id,
        name: name,
        email: data.user.email || '',
        emailVerification: data.user.email_confirmed_at !== null,
        prefs: data.user.user_metadata || {}
      };
    } catch (error) {
      console.error('Update name failed:', error);
      throw error;
    }
  }

  // Update user email
  async updateEmail(email: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.updateUser({
        email: email
      });
      if (error) throw error;
      if (!data.user) throw new Error('Failed to update email');

      return {
        id: data.user.id,
        name: data.user.user_metadata?.name || email.split('@')[0],
        email: email,
        emailVerification: data.user.email_confirmed_at !== null,
        prefs: data.user.user_metadata || {}
      };
    } catch (error) {
      console.error('Update email failed:', error);
      throw error;
    }
  }

  // Update user password
  async updatePassword(newPassword: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword
      });
      if (error) throw error;
      if (!data.user) throw new Error('Failed to update password');

      return {
        id: data.user.id,
        name: data.user.user_metadata?.name || data.user.email?.split('@')[0] || 'User',
        email: data.user.email || '',
        emailVerification: data.user.email_confirmed_at !== null,
        prefs: data.user.user_metadata || {}
      };
    } catch (error) {
      console.error('Update password failed:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const authService = new AuthService();
