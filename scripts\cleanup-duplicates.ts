/**
 * <PERSON><PERSON><PERSON> to clean up duplicate users
 * Run this in your browser console or as a Node.js script
 */

import { adminUtils } from '../lib/admin-utils';

async function cleanupDuplicates() {
  console.log('🔍 Scanning for duplicate users...');
  
  try {
    // First, let's see what duplicates we have
    const duplicates = await adminUtils.findAllDuplicateUsers();
    
    if (duplicates.length === 0) {
      console.log('✅ No duplicate users found!');
      return;
    }
    
    console.log(`⚠️ Found ${duplicates.length} emails with duplicate users:`);
    duplicates.forEach(dup => {
      console.log(`  📧 ${dup.email}: ${dup.count} users`);
      dup.profiles.forEach((profile, index) => {
        console.log(`    ${index === 0 ? '👑' : '🗑️'} ${profile.username} (${profile.$id}) - Created: ${profile.createdAt}`);
      });
    });
    
    // Ask for confirmation
    const shouldCleanup = confirm(`Found ${duplicates.length} duplicate email groups. Clean them up? This will keep the oldest profile for each email and delete the rest.`);
    
    if (!shouldCleanup) {
      console.log('❌ Cleanup cancelled by user');
      return;
    }
    
    console.log('🧹 Starting cleanup...');
    const results = await adminUtils.cleanupAllDuplicateUsers();
    
    console.log('✅ Cleanup completed!');
    console.log(`📊 Results:
    - Processed: ${results.processed} email groups
    - Merged: ${results.merged} primary profiles kept
    - Deleted: ${results.deleted} duplicate profiles
    - Errors: ${results.errors.length}`);
    
    if (results.errors.length > 0) {
      console.log('❌ Errors encountered:');
      results.errors.forEach(error => console.log(`  - ${error}`));
    }
    
  } catch (error) {
    console.error('💥 Cleanup failed:', error);
  }
}

// Export for use in browser console
(window as any).cleanupDuplicates = cleanupDuplicates;

// Auto-run if in Node.js environment
if (typeof window === 'undefined') {
  cleanupDuplicates();
}

export { cleanupDuplicates };
