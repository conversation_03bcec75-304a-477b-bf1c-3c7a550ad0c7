'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useDataSync } from '@/hooks/use-data-sync';
import { useAppStore } from '@/lib/stores/app-store';
import { Save, Check, AlertCircle, Loader2, Cloud, CloudOff } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface SaveButtonProps {
  variant?: 'default' | 'outline' | 'ghost' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  showStatus?: boolean;
  className?: string;
}

export function SaveButton({ 
  variant = 'default', 
  size = 'md', 
  showText = true,
  showStatus = true,
  className 
}: SaveButtonProps) {
  const { saveData, status, canSave } = useDataSync();
  const { addNotification } = useAppStore();
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSave = async () => {
    const success = await saveData();

    if (success) {
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Changes Saved',
        message: 'Your changes have been saved to Appwrite successfully!',
        duration: 3000
      });
    } else {
      // Show error notification
      addNotification({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save changes to Appwrite. Please try again.',
        duration: 5000
      });
    }
  };

  const getIcon = () => {
    if (status.isSaving) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (showSuccess) {
      return <Check className="h-4 w-4" />;
    }
    
    if (status.error) {
      return <AlertCircle className="h-4 w-4" />;
    }
    
    if (status.hasUnsavedChanges) {
      return <CloudOff className="h-4 w-4" />;
    }
    
    return <Save className="h-4 w-4" />;
  };

  const getText = () => {
    if (status.isSaving) return 'Saving...';
    if (showSuccess) return 'Saved!';
    if (status.error) return 'Error';
    if (status.hasUnsavedChanges) return 'Save Changes';
    return 'Saved';
  };

  const getTooltipText = () => {
    if (status.isSaving) return 'Saving your changes...';
    if (showSuccess) return 'All changes saved successfully!';
    if (status.error) return `Error: ${status.error}`;
    if (status.hasUnsavedChanges) return 'You have unsaved changes';
    if (status.lastSaved) return `Last saved: ${status.lastSaved.toLocaleTimeString()}`;
    return 'All changes are saved';
  };

  const getVariant = () => {
    if (status.error) return 'destructive';
    if (showSuccess) return 'default';
    if (status.hasUnsavedChanges) return 'default';
    return variant === 'default' ? 'outline' : variant;
  };

  const buttonContent = (
    <Button
      onClick={handleSave}
      disabled={!canSave}
      variant={getVariant() as any}
      size={size}
      className={cn(
        'transition-all duration-200',
        status.hasUnsavedChanges && 'animate-pulse',
        showSuccess && 'bg-green-600 hover:bg-green-700',
        status.error && 'bg-red-600 hover:bg-red-700',
        className
      )}
    >
      {getIcon()}
      {showText && variant !== 'icon' && (
        <span className="ml-2">{getText()}</span>
      )}
    </Button>
  );

  if (showStatus) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {buttonContent}
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    );
  }

  return buttonContent;
}

// Auto-save indicator component
export function AutoSaveIndicator() {
  const { status } = useDataSync();

  if (!status.hasUnsavedChanges && !status.isSaving && !status.lastSaved) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      {status.isSaving ? (
        <>
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Saving...</span>
        </>
      ) : status.hasUnsavedChanges ? (
        <>
          <CloudOff className="h-3 w-3" />
          <span>Unsaved changes</span>
        </>
      ) : status.lastSaved ? (
        <>
          <Cloud className="h-3 w-3" />
          <span>Saved {status.lastSaved.toLocaleTimeString()}</span>
        </>
      ) : null}
    </div>
  );
}

// Save status bar component
export function SaveStatusBar() {
  const { status, saveData, clearError } = useDataSync();

  if (!status.error && !status.hasUnsavedChanges && !status.isSaving) {
    return null;
  }

  return (
    <div className={cn(
      'fixed bottom-4 right-4 z-50 flex items-center gap-3 rounded-lg border px-4 py-2 shadow-lg backdrop-blur-sm',
      status.error ? 'border-red-200 bg-red-50/90 text-red-800' :
      status.hasUnsavedChanges ? 'border-orange-200 bg-orange-50/90 text-orange-800' :
      'border-blue-200 bg-blue-50/90 text-blue-800'
    )}>
      {status.isSaving ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Saving changes...</span>
        </>
      ) : status.error ? (
        <>
          <AlertCircle className="h-4 w-4" />
          <span>Failed to save: {status.error}</span>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              clearError();
              saveData();
            }}
            className="ml-2"
          >
            Retry
          </Button>
        </>
      ) : status.hasUnsavedChanges ? (
        <>
          <CloudOff className="h-4 w-4" />
          <span>You have unsaved changes</span>
          <Button
            size="sm"
            onClick={saveData}
            className="ml-2"
          >
            Save Now
          </Button>
        </>
      ) : null}
    </div>
  );
}

// Quick save keyboard shortcut component
export function SaveShortcut() {
  const { saveData } = useDataSync();

  // Handle Ctrl+S / Cmd+S
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        saveData();
      }
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [saveData]);

  return null;
}
