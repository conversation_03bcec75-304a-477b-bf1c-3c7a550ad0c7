import React from 'react';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from './button';
import { Card, CardContent } from './card';
import { Alert, AlertDescription } from './alert';

// Generic loading spinner
export const LoadingSpinner = ({ size = 'default', className = '' }: { 
  size?: 'sm' | 'default' | 'lg'; 
  className?: string; 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    default: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  );
};

// Loading skeleton for cards
export const CardSkeleton = ({ className = '' }: { className?: string }) => (
  <Card className={`animate-pulse ${className}`}>
    <CardContent className="p-6">
      <div className="space-y-4">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </CardContent>
  </Card>
);

// Loading skeleton for links
export const LinkSkeleton = () => (
  <div className="animate-pulse">
    <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
      <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
      <div className="flex-1 space-y-2">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
      </div>
      <div className="w-8 h-8 bg-gray-200 rounded"></div>
    </div>
  </div>
);

// Loading skeleton for profile
export const ProfileSkeleton = () => (
  <div className="animate-pulse">
    <div className="flex items-center space-x-4 p-6">
      <div className="w-20 h-20 bg-gray-200 rounded-full"></div>
      <div className="flex-1 space-y-2">
        <div className="h-6 bg-gray-200 rounded w-1/2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
      </div>
    </div>
  </div>
);

// Error boundary component
export const ErrorFallback = ({ 
  error, 
  resetError, 
  title = "Something went wrong",
  showRetry = true 
}: { 
  error: Error; 
  resetError?: () => void;
  title?: string;
  showRetry?: boolean;
}) => (
  <Alert variant="destructive" className="my-4">
    <AlertCircle className="h-4 w-4" />
    <AlertDescription className="flex items-center justify-between">
      <div>
        <div className="font-medium">{title}</div>
        <div className="text-sm mt-1">{error.message}</div>
      </div>
      {showRetry && resetError && (
        <Button 
          variant="outline" 
          size="sm" 
          onClick={resetError}
          className="ml-4"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

// Loading state for entire sections
export const SectionLoading = ({ 
  title, 
  description,
  showSpinner = true 
}: { 
  title?: string; 
  description?: string;
  showSpinner?: boolean;
}) => (
  <div className="flex flex-col items-center justify-center py-12 text-center">
    {showSpinner && <LoadingSpinner size="lg" className="mb-4 text-gray-400" />}
    {title && <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>}
    {description && <p className="text-gray-500 max-w-sm">{description}</p>}
  </div>
);

// Empty state component
export const EmptyState = ({ 
  title, 
  description, 
  action,
  icon: Icon
}: { 
  title: string; 
  description?: string; 
  action?: React.ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
}) => (
  <div className="flex flex-col items-center justify-center py-12 text-center">
    {Icon && <Icon className="w-12 h-12 text-gray-400 mb-4" />}
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    {description && <p className="text-gray-500 max-w-sm mb-6">{description}</p>}
    {action}
  </div>
);

// Cached data wrapper with loading and error states
export const CachedDataWrapper = ({ 
  isLoading, 
  error, 
  data, 
  children, 
  loadingSkeleton,
  emptyState,
  onRetry 
}: {
  isLoading: boolean;
  error?: string | null;
  data?: any;
  children: React.ReactNode;
  loadingSkeleton?: React.ReactNode;
  emptyState?: React.ReactNode;
  onRetry?: () => void;
}) => {
  if (error) {
    return (
      <ErrorFallback 
        error={new Error(error)} 
        resetError={onRetry}
        showRetry={!!onRetry}
      />
    );
  }

  if (isLoading) {
    return loadingSkeleton || <SectionLoading title="Loading..." />;
  }

  if (!data || (Array.isArray(data) && data.length === 0)) {
    return emptyState || <EmptyState title="No data available" />;
  }

  return <>{children}</>;
};

// Smart loading component that shows cached data while refreshing
export const SmartLoading = ({ 
  isLoading, 
  hasData, 
  children,
  loadingComponent,
  showRefreshIndicator = true
}: {
  isLoading: boolean;
  hasData: boolean;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  showRefreshIndicator?: boolean;
}) => {
  if (isLoading && !hasData) {
    return loadingComponent || <SectionLoading />;
  }

  return (
    <div className="relative">
      {children}
      {isLoading && hasData && showRefreshIndicator && (
        <div className="absolute top-2 right-2">
          <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-sm">
            <LoadingSpinner size="sm" className="text-blue-500" />
          </div>
        </div>
      )}
    </div>
  );
};

// Progress indicator for multi-step loading
export const LoadingProgress = ({ 
  steps, 
  currentStep, 
  className = '' 
}: { 
  steps: string[]; 
  currentStep: number; 
  className?: string; 
}) => (
  <div className={`space-y-4 ${className}`}>
    <div className="flex items-center justify-center">
      <LoadingSpinner size="lg" className="text-blue-500" />
    </div>
    <div className="text-center">
      <div className="text-sm font-medium text-gray-900 mb-2">
        {steps[currentStep] || 'Loading...'}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
        />
      </div>
      <div className="text-xs text-gray-500 mt-1">
        Step {currentStep + 1} of {steps.length}
      </div>
    </div>
  </div>
);

// Retry button component
export const RetryButton = ({ 
  onRetry, 
  isRetrying = false, 
  className = '' 
}: { 
  onRetry: () => void; 
  isRetrying?: boolean; 
  className?: string; 
}) => (
  <Button 
    variant="outline" 
    onClick={onRetry} 
    disabled={isRetrying}
    className={className}
  >
    <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
    {isRetrying ? 'Retrying...' : 'Retry'}
  </Button>
);
