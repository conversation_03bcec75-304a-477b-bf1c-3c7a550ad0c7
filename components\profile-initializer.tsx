'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useUserStore } from '@/lib/stores/user-store';
import { userService } from '@/lib/user-service';
import { linksService } from '@/lib/links-service';

export function ProfileInitializer() {
  const { user, isAuthenticated } = useAuthStore();
  const { profile, setProfile, reorderLinks, setLinksLoading } = useUserStore();
  const [isInitializing, setIsInitializing] = useState(false);

  useEffect(() => {
    const initializeProfile = async () => {
      console.log('🔄 ProfileInitializer useEffect triggered');
      console.log('🔐 isAuthenticated:', isAuthenticated);
      console.log('👤 user:', user);
      console.log('📋 profile:', profile);
      console.log('⏳ isInitializing:', isInitializing);

      if (!isAuthenticated) {
        console.log('❌ User not authenticated');
        return;
      }

      if (!user) {
        console.log('❌ No user object');
        return;
      }

      if (profile) {
        console.log('❌ Profile already exists, skipping initialization');
        console.log('📋 Existing profile:', profile);
        // Still load links even if profile exists
        console.log('🔗 Loading user links for existing profile...');
        setLinksLoading(true);
        try {
          const userLinks = await linksService.getUserLinks(user.id);
          console.log('🔗 Raw links from Supabase:', userLinks);

          if (userLinks && userLinks.length > 0) {
            const storeLinks = userLinks.map(link => ({
              id: link.id!,
              title: link.title,
              url: link.url,
              description: link.description || '',
              icon: link.icon || '',
              backgroundColor: link.background_color || '',
              textColor: link.text_color || '',
              borderColor: link.border_color || '',
              customStyle: link.custom_style || '',
              clicks: link.clicks || 0,
              isActive: link.is_active,
              order: link.order
            }));

            reorderLinks(storeLinks);
            console.log(`✅ Loaded ${userLinks.length} links into store`);
          } else {
            console.log('📝 No existing links found - user will start with empty links');
          }
        } catch (error) {
          console.error('❌ Failed to load links for existing profile:', error);
        } finally {
          setLinksLoading(false);
        }
        return;
      }

      if (isInitializing) {
        console.log('❌ Already initializing');
        return;
      }

      try {
        setIsInitializing(true);
        console.log('🔍 Checking if user profile exists...');
        console.log('🆔 User ID:', user.id);

        // Check if profile exists
        const existingProfile = await userService.getUserProfile(user.id);

        if (existingProfile) {
          console.log('✅ Found existing profile, loading...');

          // Convert to store format and reconstruct design object
          const storeProfile = {
            id: existingProfile.user_id,
            name: existingProfile.name,
            username: existingProfile.username,
            bio: existingProfile.bio,
            avatar: existingProfile.avatar_url,
            email: existingProfile.email,
            template: existingProfile.template,
            customColors: existingProfile.custom_colors,
            backgroundImage: existingProfile.background_image,
            backgroundColor: existingProfile.background_color,
            backgroundType: existingProfile.background_type,
            gradientColors: existingProfile.gradient_colors,
            fontFamily: existingProfile.font_family,
            fontSize: existingProfile.font_size,
            borderRadius: existingProfile.border_radius,
            glassmorphism: existingProfile.glassmorphism,
            customCSS: existingProfile.custom_css,
            socialLinksStyle: existingProfile.socialLinksStyle,
            defaultLinkStyle: existingProfile.defaultLinkStyle,
            // Reconstruct design object from flat fields
            design: {
              background: {
                type: existingProfile.backgroundType || 'color',
                value: (() => {
                  const bgType = existingProfile.backgroundType || 'color';
                  // Only use image if backgroundType is 'image' AND backgroundImage exists
                  if (bgType === 'image' && existingProfile.backgroundImage) {
                    // Add url() wrapper if it's an image URL and doesn't already have it
                    const imageUrl = existingProfile.backgroundImage;
                    return imageUrl.startsWith('url(') ? imageUrl : `url(${imageUrl})`;
                  }
                  // Only use color if backgroundType is 'color' AND backgroundColor exists
                  else if (bgType === 'color' && existingProfile.backgroundColor) {
                    return existingProfile.backgroundColor;
                  }
                  // Default fallback
                  else if (bgType === 'color') {
                    return 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500';
                  } else {
                    return ''; // For 'none' type
                  }
                })(),
                customColor: existingProfile.backgroundType === 'color' ? existingProfile.backgroundColor : undefined
              },
              typography: {
                fontFamily: existingProfile.fontFamily || 'font-sans',
                fontSize: 16,
                textColor: 'text-white'
              },
              linkCards: {
                style: 'glass',
                size: 60
              },
              effects: {
                animations: true,
                glassmorphism: existingProfile.glassmorphism || false,
                shadows: true
              }
            }
          };

          setProfile(storeProfile);
          console.log('✅ Profile loaded into store');
        } else {
          console.log('📝 No existing profile found - user will start with empty state');
          // Don't create a profile automatically - let user create it manually
        }

        // Load links regardless of whether profile exists
        console.log('🔗 Loading user links for user ID:', user.id);
        setLinksLoading(true);

        const userLinks = await linksService.getUserLinks(user.id);
        console.log('🔗 Raw links from Supabase:', userLinks);

        if (userLinks && userLinks.length > 0) {
          // Convert Supabase links to store format
          const storeLinks = userLinks.map(link => ({
            id: link.id!,
            title: link.title,
            url: link.url,
            description: link.description || '',
            icon: link.icon || '',
            backgroundColor: link.background_color || '',
            textColor: link.text_color || '',
            borderColor: link.border_color || '',
            customStyle: link.custom_style || '',
            clicks: link.clicks || 0,
            isActive: link.is_active,
            order: link.order
          }));

          reorderLinks(storeLinks);
          console.log(`✅ Loaded ${userLinks.length} links into store`);
        } else {
          console.log('📝 No existing links found - user will start with empty links');
        }
        setLinksLoading(false);

      } catch (error) {
        console.error('❌ Failed to initialize profile:', error);
        setLinksLoading(false);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeProfile();
  }, [isAuthenticated, user, profile, setProfile, reorderLinks, isInitializing]);

  // This component doesn't render anything
  return null;
}
