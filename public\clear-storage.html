<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear LinkVibe Storage</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .success {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }
        .storage-info {
            font-family: monospace;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 LinkVibe Storage Cleaner</h1>
        
        <div class="info">
            <h3>What does this do?</h3>
            <p>This tool helps you clear your browser's local storage if you're experiencing quota exceeded errors in LinkVibe.</p>
            <ul>
                <li><strong>Selective Clean:</strong> Removes only old and large cached data</li>
                <li><strong>Complete Reset:</strong> Clears all LinkVibe data (you'll need to log in again)</li>
            </ul>
        </div>

        <div class="storage-info" id="storageInfo">
            <div>Calculating storage usage...</div>
        </div>

        <button onclick="selectiveClean()" id="selectiveBtn">
            🧽 Selective Clean (Recommended)
        </button>

        <button onclick="completeReset()" id="resetBtn">
            🗑️ Complete Reset (Nuclear Option)
        </button>

        <button onclick="goBack()">
            ← Back to LinkVibe
        </button>

        <div class="success" id="successMessage">
            ✅ Storage cleaned successfully! You can now go back to LinkVibe.
        </div>
    </div>

    <script>
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getStorageUsage() {
            let used = 0;
            let linkvibeUsed = 0;
            let itemCount = 0;
            let linkvibeItemCount = 0;

            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    const size = localStorage[key].length + key.length;
                    used += size;
                    itemCount++;
                    
                    if (key.startsWith('linkvibe-')) {
                        linkvibeUsed += size;
                        linkvibeItemCount++;
                    }
                }
            }

            return { used, linkvibeUsed, itemCount, linkvibeItemCount };
        }

        function updateStorageInfo() {
            const usage = getStorageUsage();
            const info = document.getElementById('storageInfo');
            
            info.innerHTML = `
                <div><strong>Total Storage Used:</strong> ${formatBytes(usage.used)} (${usage.itemCount} items)</div>
                <div><strong>LinkVibe Data:</strong> ${formatBytes(usage.linkvibeUsed)} (${usage.linkvibeItemCount} items)</div>
                <div><strong>Other Data:</strong> ${formatBytes(usage.used - usage.linkvibeUsed)} (${usage.itemCount - usage.linkvibeItemCount} items)</div>
            `;
        }

        function selectiveClean() {
            const btn = document.getElementById('selectiveBtn');
            btn.disabled = true;
            btn.textContent = 'Cleaning...';

            let freedSpace = 0;
            const itemsToRemove = [];

            // Find large items and old LinkVibe data
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    const value = localStorage[key];
                    const size = value.length + key.length;
                    
                    // Remove large items (> 100KB)
                    if (size > 100 * 1024) {
                        itemsToRemove.push({ key, size, reason: 'large' });
                    }
                    
                    // Remove old or corrupted LinkVibe data
                    if (key.startsWith('linkvibe-')) {
                        try {
                            const parsed = JSON.parse(value);
                            if (!parsed || typeof parsed !== 'object') {
                                itemsToRemove.push({ key, size, reason: 'corrupted' });
                            }
                        } catch (e) {
                            itemsToRemove.push({ key, size, reason: 'corrupted' });
                        }
                    }
                }
            }

            // Remove items
            itemsToRemove.forEach(({ key, size }) => {
                localStorage.removeItem(key);
                freedSpace += size;
            });

            setTimeout(() => {
                btn.disabled = false;
                btn.textContent = '🧽 Selective Clean (Recommended)';
                updateStorageInfo();
                showSuccess(`Cleaned ${itemsToRemove.length} items, freed ${formatBytes(freedSpace)}`);
            }, 1000);
        }

        function completeReset() {
            if (confirm('Are you sure? This will remove ALL LinkVibe data and log you out.')) {
                const btn = document.getElementById('resetBtn');
                btn.disabled = true;
                btn.textContent = 'Resetting...';

                const usage = getStorageUsage();
                
                // Remove all LinkVibe data
                const keysToRemove = [];
                for (let key in localStorage) {
                    if (key.startsWith('linkvibe-')) {
                        keysToRemove.push(key);
                    }
                }

                keysToRemove.forEach(key => localStorage.removeItem(key));

                setTimeout(() => {
                    btn.disabled = false;
                    btn.textContent = '🗑️ Complete Reset (Nuclear Option)';
                    updateStorageInfo();
                    showSuccess(`Reset complete! Removed ${keysToRemove.length} LinkVibe items, freed ${formatBytes(usage.linkvibeUsed)}`);
                }, 1000);
            }
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.innerHTML = `✅ ${message}`;
            successDiv.style.display = 'block';
        }

        function goBack() {
            window.location.href = '/';
        }

        // Initialize
        updateStorageInfo();
    </script>
</body>
</html>
