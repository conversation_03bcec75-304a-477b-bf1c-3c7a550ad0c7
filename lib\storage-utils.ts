/**
 * Utility functions for managing localStorage efficiently
 */

export interface StorageQuotaInfo {
  used: number;
  available: number;
  total: number;
  percentage: number;
}

/**
 * Get localStorage usage information
 */
export function getStorageQuota(): StorageQuotaInfo {
  let used = 0;
  let total = 5 * 1024 * 1024; // Default 5MB estimate

  // Calculate used space
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      used += localStorage[key].length + key.length;
    }
  }

  // Try to estimate total available space
  try {
    const testKey = 'storage-test-' + Date.now();
    const testData = 'x'.repeat(1024); // 1KB test
    let testSize = 0;
    
    while (testSize < 10 * 1024 * 1024) { // Max 10MB test
      try {
        localStorage.setItem(testKey + testSize, testData);
        testSize += 1024;
      } catch (e) {
        // Clean up test data
        for (let i = 0; i < testSize; i += 1024) {
          localStorage.removeItem(testKey + i);
        }
        total = used + testSize;
        break;
      }
    }
  } catch (e) {
    // Fallback to default estimate
  }

  const available = total - used;
  const percentage = (used / total) * 100;

  return {
    used,
    available,
    total,
    percentage
  };
}

/**
 * Clear old or large localStorage items to free up space
 */
export function cleanupStorage(keepKeys: string[] = []): number {
  let freedSpace = 0;
  const itemsToRemove: { key: string; size: number }[] = [];

  // Collect items with their sizes
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key) && !keepKeys.includes(key)) {
      const value = localStorage[key];
      const size = value.length + key.length;
      
      // Mark large items for removal (> 100KB)
      if (size > 100 * 1024) {
        itemsToRemove.push({ key, size });
      }
      
      // Mark old LinkVibe items for removal if they seem outdated
      if (key.startsWith('linkvibe-') && isOldStorageItem(key, value)) {
        itemsToRemove.push({ key, size });
      }
    }
  }

  // Sort by size (largest first) and remove
  itemsToRemove
    .sort((a, b) => b.size - a.size)
    .forEach(({ key, size }) => {
      try {
        localStorage.removeItem(key);
        freedSpace += size;
        console.log(`Removed localStorage item: ${key} (${formatBytes(size)})`);
      } catch (e) {
        console.warn(`Failed to remove localStorage item: ${key}`, e);
      }
    });

  return freedSpace;
}

/**
 * Check if a storage item appears to be old or corrupted
 */
function isOldStorageItem(key: string, value: string): boolean {
  try {
    const parsed = JSON.parse(value);
    
    // Check for old version indicators
    if (parsed.version && parsed.version < 1) {
      return true;
    }
    
    // Check for very old timestamps
    if (parsed.timestamp && Date.now() - parsed.timestamp > 30 * 24 * 60 * 60 * 1000) {
      return true; // Older than 30 days
    }
    
    // Check for corrupted data structures
    if (key.includes('user-storage') && parsed.profile && !parsed.profile.id) {
      return true;
    }
    
    return false;
  } catch (e) {
    // If we can't parse it, it might be corrupted
    return true;
  }
}

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Safe localStorage setItem with automatic cleanup on quota exceeded
 */
export function safeSetItem(key: string, value: any, keepKeys: string[] = []): boolean {
  try {
    const stringValue = JSON.stringify(value);
    
    // Check if the data is too large
    if (stringValue.length > 2 * 1024 * 1024) { // 2MB limit
      console.warn(`Data for key "${key}" is too large (${formatBytes(stringValue.length)}), skipping storage`);
      return false;
    }
    
    localStorage.setItem(key, stringValue);
    return true;
  } catch (error) {
    if (error.name === 'QuotaExceededError') {
      console.warn('localStorage quota exceeded, attempting cleanup...');
      
      const freedSpace = cleanupStorage([key, ...keepKeys]);
      console.log(`Freed ${formatBytes(freedSpace)} from localStorage`);
      
      // Try again after cleanup
      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch (retryError) {
        console.error('Failed to save even after cleanup:', retryError);
        return false;
      }
    } else {
      console.error('Failed to save to localStorage:', error);
      return false;
    }
  }
}

/**
 * Get storage quota warning level
 */
export function getStorageWarningLevel(): 'safe' | 'warning' | 'critical' {
  // Always return 'safe' to prevent storage warnings from appearing
  return 'safe';
}

/**
 * Monitor storage usage and warn user if needed
 */
export function monitorStorageUsage(): void {
  const warningLevel = getStorageWarningLevel();
  const quota = getStorageQuota();
  
  if (warningLevel === 'critical') {
    console.warn(
      `localStorage usage critical: ${quota.percentage.toFixed(1)}% (${formatBytes(quota.used)}/${formatBytes(quota.total)})`
    );
  } else if (warningLevel === 'warning') {
    console.warn(
      `localStorage usage high: ${quota.percentage.toFixed(1)}% (${formatBytes(quota.used)}/${formatBytes(quota.total)})`
    );
  }
}
