import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService, User } from '../auth';
import { userService, UserProfile } from '../user-service';
import { cachedAPI } from '../cached-api';

interface AuthState {
  user: User | null;
  userProfile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  lastAuthCheck: number | null;
  authCheckInterval: number;

  // Actions
  setUser: (user: User | null, userProfile?: UserProfile | null) => void;
  setLoading: (loading: boolean) => void;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: (forceRefresh?: boolean) => Promise<void>;
  sendPasswordRecovery: (email: string) => Promise<void>;
  resetPassword: (accessToken: string, refreshToken: string, password: string) => Promise<void>;
  sendEmailVerification: () => Promise<void>;
  verifyEmail: (token: string, type: string) => Promise<void>;
  updateProfile: (updates: { name?: string; email?: string; password?: string; oldPassword?: string }) => Promise<void>;
  refreshUserProfile: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      userProfile: null,
      isLoading: false,
      isAuthenticated: false,
      lastAuthCheck: null,
      authCheckInterval: 5 * 60 * 1000, // 5 minutes

      setUser: (user, userProfile) => set({
        user,
        userProfile: userProfile || null,
        isAuthenticated: !!user
      }),

      setLoading: (loading) => set({ isLoading: loading }),

      login: async (email: string, password: string) => {
        set({ isLoading: true });
        try {
          await authService.login(email, password);
          const user = await authService.getCurrentUser();

          if (user) {
            // Get or create user profile using cached API
            const userProfile = await cachedAPI.getOrCreateUserProfile(user);

            set({
              user,
              userProfile,
              isAuthenticated: true,
              isLoading: false,
              lastAuthCheck: Date.now()
            });
          }
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      loginWithGoogle: async () => {
        set({ isLoading: true });
        try {
          await authService.loginWithGoogle();
          // Note: After OAuth redirect, checkAuth will be called to update state
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (email: string, password: string, name: string) => {
        set({ isLoading: true });
        try {
          const user = await authService.createAccount(email, password, name);

          if (user) {
            // Create user profile
            const userProfile = await userService.createUserProfile(user);

            set({
              user,
              userProfile,
              isAuthenticated: true,
              isLoading: false,
              lastAuthCheck: Date.now()
            });
          }
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await authService.logout();
          // Clear cache when logging out
          cachedAPI.clearAllCache();

          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            lastAuthCheck: null
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      checkAuth: async (forceRefresh = false) => {
        const state = get();
        const now = Date.now();

        // Skip auth check if recently checked (unless forced)
        if (!forceRefresh && state.lastAuthCheck &&
            (now - state.lastAuthCheck) < state.authCheckInterval) {
          console.log('🔐 AuthStore: Skipping auth check (recently checked)');
          return;
        }

        console.log('🔐 AuthStore: Starting auth check...');
        set({ isLoading: true });
        try {
          const user = await authService.getCurrentUser();
          console.log('🔐 AuthStore: Current user:', user);

          if (user) {
            // Get or create user profile using cached API
            console.log('🔐 AuthStore: Getting user profile...');
            const userProfile = await cachedAPI.getOrCreateUserProfile(user, forceRefresh);
            console.log('🔐 AuthStore: User profile:', userProfile);

            set({
              user,
              userProfile,
              isAuthenticated: true,
              isLoading: false,
              lastAuthCheck: now
            });
            console.log('✅ AuthStore: User authenticated successfully');
          } else {
            console.log('❌ AuthStore: No user found');
            set({
              user: null,
              userProfile: null,
              isAuthenticated: false,
              isLoading: false,
              lastAuthCheck: now
            });
          }
        } catch (error) {
          console.error('❌ AuthStore: Auth check failed:', error);
          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },

      sendPasswordRecovery: async (email: string) => {
        set({ isLoading: true });
        try {
          await authService.sendPasswordRecovery(email);
          set({ isLoading: false });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      resetPassword: async (accessToken: string, refreshToken: string, password: string) => {
        set({ isLoading: true });
        try {
          await authService.completePasswordRecovery(accessToken, refreshToken, password);
          set({ isLoading: false });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      sendEmailVerification: async () => {
        set({ isLoading: true });
        try {
          await authService.sendEmailVerification();
          set({ isLoading: false });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      verifyEmail: async (token: string, type: string) => {
        set({ isLoading: true });
        try {
          await authService.verifyEmail(token, type);
          const user = await authService.getCurrentUser();
          set({
            user,
            isLoading: false
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      updateProfile: async (updates) => {
        set({ isLoading: true });
        try {
          let user = get().user;
          
          if (updates.name && user) {
            user = await authService.updateName(updates.name);
          }
          
          if (updates.email && updates.oldPassword && user) {
            user = await authService.updateEmail(updates.email, updates.oldPassword);
          }
          
          if (updates.password && updates.oldPassword && user) {
            user = await authService.updatePassword(updates.password, updates.oldPassword);
          }
          
          set({ 
            user, 
            isLoading: false 
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      refreshUserProfile: async () => {
        const state = get();
        if (!state.user) return;

        try {
          console.log('🔄 Refreshing user profile...');
          const userProfile = await cachedAPI.getOrCreateUserProfile(state.user, true);
          set({ userProfile });
          console.log('✅ User profile refreshed');
        } catch (error) {
          console.error('❌ Failed to refresh user profile:', error);
        }
      },
    }),
    {
      name: 'linkvibe-auth-storage',
      partialize: (state) => ({
        // Only persist essential user data
        user: state.user ? {
          id: state.user.id,
          name: state.user.name,
          email: state.user.email,
          emailVerification: state.user.emailVerification,
        } : null,
        userProfile: state.userProfile ? {
          id: state.userProfile.id,
          user_id: state.userProfile.user_id,
          username: state.userProfile.username,
          name: state.userProfile.name,
          email: state.userProfile.email,
        } : null,
        isAuthenticated: state.isAuthenticated,
      }),
      // Use SSR-safe storage
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);
