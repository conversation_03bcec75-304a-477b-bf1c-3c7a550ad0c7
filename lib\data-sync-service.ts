import { userService, UserProfile } from './user-service';
import { linksService, LinkData } from './links-service';
import { fileUploadService } from './file-upload-service';
import { cleanupImagePreview } from './image-utils';

export interface CompleteUserData {
  profile: UserProfile;
  links: LinkData[];
  socialLinks: any[];
}

export class DataSyncService {
  // Save all user data to Supabase
  async saveAllUserData(userId: string, profile: any, links: any[]): Promise<void> {
    try {
      if (!userId || !profile) {
        throw new Error('User ID and profile are required');
      }

      console.log('🔄 Starting complete data sync...');

      // 1. Save profile with all styling and customization
      await this.saveProfileData(userId, profile);

      // 2. Save all links with their individual styling
      await this.saveLinksData(userId, links);

      console.log('✅ Complete data sync successful');
    } catch (error) {
      console.error('❌ Failed to save all user data:', error);
      throw error;
    }
  }

  // Save profile data including all styling options
  async saveProfileData(userId: string, profile: any): Promise<UserProfile> {
    try {
      console.log('💾 Saving profile data for user:', userId);
      console.log('📊 Profile data:', profile);
      console.log('🖼️ Avatar info:', {
        avatar: profile.avatar,
        avatarFile: profile.avatarFile ? 'File object present' : 'No file',
        avatarFileType: profile.avatarFile?.type,
        avatarFileSize: profile.avatarFile?.size
      });

      // Handle file uploads first
      let avatarUrl = profile.avatar;
      let backgroundImageUrl = profile.design?.background?.value;

      // Upload avatar if it's a blob URL (temporary)
      if (profile.avatarFile && profile.avatar?.startsWith('blob:')) {
        try {
          console.log('📤 Uploading avatar to storage...');
          const uploadResult = await fileUploadService.uploadAvatar(profile.avatarFile, userId);
          avatarUrl = uploadResult.url;

          // Clean up the blob URL
          cleanupImagePreview(profile.avatar);
          console.log('✅ Avatar uploaded successfully');
        } catch (error) {
          console.error('❌ Failed to upload avatar:', error);
          // Keep the blob URL as fallback, but this will be temporary
        }
      }

      // Upload background image if it's a blob URL (temporary)
      if (profile.backgroundImageFile && backgroundImageUrl?.includes('blob:')) {
        try {
          console.log('📤 Uploading background image to storage...');
          const uploadResult = await fileUploadService.uploadBackground(profile.backgroundImageFile, userId);
          backgroundImageUrl = `url(${uploadResult.url})`;

          // Clean up the blob URL
          const urlMatch = profile.design.background.value.match(/url\(([^)]+)\)/);
          if (urlMatch) {
            cleanupImagePreview(urlMatch[1]);
          }
          console.log('✅ Background image uploaded successfully');
        } catch (error) {
          console.error('❌ Failed to upload background image:', error);
          // Keep the blob URL as fallback, but this will be temporary
        }
      }

      // First check if profile exists, if not create it
      let existingProfile = await userService.getUserProfile(userId);

      if (!existingProfile) {
        console.log('📝 Profile not found, creating new profile...');
        // Create a new profile with basic auth user data
        const authUser = {
          $id: userId,
          name: profile.name || 'User',
          email: profile.email || ''
        };
        existingProfile = await userService.createUserProfile(authUser);
      }

      // Transform design data to flat structure for Appwrite
      let backgroundColor = profile.backgroundColor;
      let backgroundType = profile.backgroundType || 'color';
      let fontFamily = profile.fontFamily;
      let customColors = profile.customColors;

      // Use profile fields directly if they exist, otherwise extract from design object
      if (profile.backgroundType) {
        backgroundType = profile.backgroundType;
      }
      if (profile.backgroundColor) {
        backgroundColor = profile.backgroundColor;
      }
      if (profile.backgroundImage) {
        backgroundImageUrl = profile.backgroundImage;
      }

      // Extract data from design object if profile fields are not set
      if (profile.design && !profile.backgroundType) {
        // Handle background
        if (profile.design.background) {
          backgroundType = profile.design.background.type;
          if (profile.design.background.type === 'color') {
            backgroundColor = profile.design.background.value;
            backgroundImageUrl = null; // Clear image when color is selected
          } else if (profile.design.background.type === 'image') {
            backgroundImageUrl = profile.design.background.value;
            backgroundColor = null; // Clear color when image is selected
          } else if (profile.design.background.type === 'none') {
            backgroundColor = null;
            backgroundImageUrl = null;
          }
        }

        // Handle typography
        if (profile.design.typography) {
          fontFamily = profile.design.typography.fontFamily;
        }

        // Handle custom colors - ensure it's a string for Appwrite
        if (profile.design.customColors) {
          customColors = typeof profile.design.customColors === 'string'
            ? profile.design.customColors
            : JSON.stringify(profile.design.customColors);
        }
      }

      // Ensure customColors is a string and within size limit
      if (customColors && typeof customColors === 'object') {
        customColors = JSON.stringify(customColors);
      }

      // Truncate customColors if it's too long (Appwrite limit is 1000 chars)
      if (customColors && customColors.length > 1000) {
        console.warn('⚠️ customColors too long, truncating...');
        customColors = customColors.substring(0, 1000);
      }

      const profileData = {
        name: profile.name,
        bio: profile.bio,
        avatar_url: avatarUrl, // Use uploaded URL or existing URL
        template: profile.template,
        custom_colors: customColors,
        background_image: backgroundImageUrl,
        background_color: backgroundColor,
        background_type: backgroundType,
        gradient_colors: profile.gradientColors,
        font_family: fontFamily,
        font_size: profile.fontSize || 'medium',
        border_radius: profile.borderRadius || 'medium',
        glassmorphism: profile.glassmorphism || false,
        custom_css: profile.customCSS,
        social_links_style: profile.socialLinksStyle || {
          position: 'bottom',
          style: 'icons',
          size: 'medium'
        },
        default_link_style: profile.defaultLinkStyle || {
          backgroundColor: '#ffffff',
          textColor: '#000000',
          borderColor: '#e5e7eb',
          borderWidth: 1,
          borderRadius: 8,
          shadow: true,
          hoverEffect: 'lift'
        }
      };

      const savedProfile = await userService.saveCompleteProfile(userId, profileData);
      console.log('✅ Profile data saved');
      return savedProfile;
    } catch (error) {
      console.error('❌ Failed to save profile data:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));
      console.error('❌ User ID was:', userId);
      console.error('❌ Profile data was:', JSON.stringify(profileData, null, 2));
      throw error;
    }
  }

  // Save links data with individual styling
  async saveLinksData(userId: string, links: any[]): Promise<LinkData[]> {
    try {
      console.log('🔗 Saving links data...');

      const linksData = links.map((link, index) => ({
        title: link.title,
        url: link.url,
        description: link.description || '',
        icon: link.icon,
        background_color: link.backgroundColor,
        text_color: link.textColor,
        border_color: link.borderColor,
        custom_style: link.customStyle,
        is_active: link.isActive,
        order: link.order !== undefined ? link.order : index,
        clicks: link.clicks || 0
      }));

      const savedLinks = await linksService.syncUserLinks(userId, linksData);
      console.log('✅ Links data saved');
      return savedLinks;
    } catch (error) {
      console.error('❌ Failed to save links data:', error);
      throw error;
    }
  }

  // Load all user data from Appwrite
  async loadAllUserData(userId: string): Promise<CompleteUserData | null> {
    try {
      console.log('📥 Loading complete user data...');

      // Load profile
      let profile = await userService.getCompleteProfile(userId);

      if (!profile) {
        console.log('📝 Profile not found, will create on first save');
        // Return null so the sync process knows to create a profile
        return null;
      }

      // Load links
      const links = await linksService.getUserLinks(userId);

      // For now, social links are stored in the profile or can be separate
      const socialLinks: any[] = [];

      console.log('✅ Complete user data loaded');
      return {
        profile,
        links,
        socialLinks
      };
    } catch (error) {
      console.error('❌ Failed to load user data:', error);
      return null;
    }
  }

  // Sync local store with Appwrite data
  async syncStoreWithAppwrite(userId: string, setProfile: any, reorderLinks: any): Promise<void> {
    try {
      console.log('🔄 Syncing store with Appwrite...');

      const userData = await this.loadAllUserData(userId);
      if (!userData) {
        console.log('📝 No existing profile found, will create on first save');
        // Don't throw error - just skip sync for now
        return;
      }

      // Convert Appwrite profile to store format and reconstruct design object
      const storeProfile = {
        id: userData.profile.userId,
        name: userData.profile.name,
        username: userData.profile.username,
        bio: userData.profile.bio,
        avatar: userData.profile.avatar,
        email: userData.profile.email,
        template: userData.profile.template,
        customColors: userData.profile.customColors,
        backgroundImage: userData.profile.backgroundImage,
        backgroundColor: userData.profile.backgroundColor,
        backgroundType: userData.profile.backgroundType,
        gradientColors: userData.profile.gradientColors,
        fontFamily: userData.profile.fontFamily,
        fontSize: userData.profile.fontSize,
        borderRadius: userData.profile.borderRadius,
        glassmorphism: userData.profile.glassmorphism,
        customCSS: userData.profile.customCSS,
        socialLinksStyle: userData.profile.socialLinksStyle,
        defaultLinkStyle: userData.profile.defaultLinkStyle,
        // Reconstruct design object from flat fields
        design: {
          background: {
            type: userData.profile.backgroundType || 'color',
            value: (() => {
              const bgType = userData.profile.backgroundType || 'color';
              if (bgType === 'image') {
                // Add url() wrapper if it's an image URL and doesn't already have it
                const imageUrl = userData.profile.backgroundImage || '';
                return imageUrl ? (imageUrl.startsWith('url(') ? imageUrl : `url(${imageUrl})`) : '';
              } else if (bgType === 'color') {
                return userData.profile.backgroundColor || 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500';
              } else {
                return ''; // For 'none' type
              }
            })(),
            customColor: userData.profile.backgroundType === 'color' ? userData.profile.backgroundColor : undefined
          },
          typography: {
            fontFamily: userData.profile.fontFamily || 'font-sans',
            fontSize: 16,
            textColor: 'text-white'
          },
          linkCards: {
            style: 'glass',
            size: 60
          },
          effects: {
            animations: true,
            glassmorphism: userData.profile.glassmorphism || false,
            shadows: true
          }
        }
      };

      // Convert Appwrite links to store format
      const storeLinks = userData.links.map(link => ({
        id: link.$id!,
        title: link.title,
        url: link.url,
        description: link.description,
        icon: link.icon,
        backgroundColor: link.backgroundColor,
        textColor: link.textColor,
        borderColor: link.borderColor,
        customStyle: link.customStyle,
        clicks: link.clicks,
        isActive: link.isActive,
        order: link.order
      }));

      setProfile(storeProfile);
      reorderLinks(storeLinks);

      console.log('✅ Store synced with Appwrite');
    } catch (error) {
      console.error('❌ Failed to sync store with Appwrite:', error);
      throw error;
    }
  }

  // Auto-save functionality (debounced)
  saveTimeout: NodeJS.Timeout | null = null;

  autoSave(userId: string, profile: any, links: any[], delay: number = 2000): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(async () => {
      try {
        await this.saveAllUserData(userId, profile, links);
        console.log('🔄 Auto-save completed');
      } catch (error) {
        console.error('❌ Auto-save failed:', error);
      }
    }, delay);
  }

  // Manual save with loading state
  async manualSave(userId: string, profile: any, links: any[], setLoading: any): Promise<boolean> {
    try {
      setLoading(true);

      await this.saveAllUserData(userId, profile, links);

      setLoading(false);
      return true;
    } catch (error) {
      setLoading(false);
      console.error('❌ Manual save failed:', error);
      return false;
    }
  }

  // Export user data (for backup or migration)
  async exportUserData(userId: string): Promise<string> {
    try {
      const userData = await this.loadAllUserData(userId);
      if (!userData) {
        throw new Error('No user data found');
      }

      return JSON.stringify(userData, null, 2);
    } catch (error) {
      console.error('❌ Failed to export user data:', error);
      throw error;
    }
  }

  // Import user data (for backup restoration)
  async importUserData(userId: string, jsonData: string, setProfile: any, reorderLinks: any): Promise<void> {
    try {
      const userData = JSON.parse(jsonData) as CompleteUserData;

      // Save imported profile
      await this.saveProfileData(userId, userData.profile);

      // Save imported links
      await this.saveLinksData(userId, userData.links);

      // Sync with store
      await this.syncStoreWithAppwrite(userId, setProfile, reorderLinks);

      console.log('✅ User data imported successfully');
    } catch (error) {
      console.error('❌ Failed to import user data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dataSyncService = new DataSyncService();
