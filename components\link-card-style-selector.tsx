'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Layout,
  Sparkles,
  Palette,
  Zap,
  Eye
} from 'lucide-react';

interface LinkCardStyle {
  id: string;
  name: string;
  description: string;
  category: string;
  cssClasses: string;
  previewColor: string;
  textColor: string;
  icon: React.ReactNode;
}

interface LinkCardStyleSelectorProps {
  selectedStyleId?: string;
  onStyleSelect: (styleId: string, styleName: string) => void;
  previewText?: string;
}

// Predefined link card styles
const PREDEFINED_STYLES: LinkCardStyle[] = [
  {
    id: 'glass',
    name: 'Glass',
    description: 'Elegant glassmorphism effect with transparency',
    category: 'Modern',
    cssClasses: 'bg-white/20 backdrop-blur-lg border border-white/30',
    previewColor: 'rgba(255, 255, 255, 0.2)',
    textColor: 'text-white',
    icon: <Sparkles className="w-4 h-4" />
  },
  {
    id: 'neon',
    name: 'Neon',
    description: 'Vibrant neon glow effect',
    category: 'Bold',
    cssClasses: 'bg-black border-2 border-cyan-400 shadow-lg shadow-cyan-400/50',
    previewColor: '#00ffff',
    textColor: 'text-white',
    icon: <Zap className="w-4 h-4" />
  },
  {
    id: 'minimal',
    name: 'Minimal',
    description: 'Clean and simple design',
    category: 'Simple',
    cssClasses: 'bg-white border border-gray-200 shadow-sm',
    previewColor: '#ffffff',
    textColor: 'text-gray-900',
    icon: <Layout className="w-4 h-4" />
  },
  {
    id: 'bold',
    name: 'Bold',
    description: 'Eye-catching gradient background',
    category: 'Bold',
    cssClasses: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0',
    previewColor: 'linear-gradient(to right, #8b5cf6, #ec4899)',
    textColor: 'text-white',
    icon: <Palette className="w-4 h-4" />
  },
  {
    id: 'soft',
    name: 'Soft',
    description: 'Gentle pastel gradient',
    category: 'Simple',
    cssClasses: 'bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-200',
    previewColor: 'linear-gradient(to right, #eff6ff, #faf5ff)',
    textColor: 'text-gray-900',
    icon: <Eye className="w-4 h-4" />
  },
  {
    id: 'dark',
    name: 'Dark',
    description: 'Sleek dark theme',
    category: 'Modern',
    cssClasses: 'bg-gray-900 border border-gray-700 text-white',
    previewColor: '#111827',
    textColor: 'text-white',
    icon: <Layout className="w-4 h-4" />
  }
];

export function LinkCardStyleSelector({
  selectedStyleId,
  onStyleSelect,
  previewText = "Sample Link"
}: LinkCardStyleSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const handleStyleSelect = (style: LinkCardStyle) => {
    onStyleSelect(style.id, style.name);
  };

  const filteredStyles = PREDEFINED_STYLES.filter(style => {
    return selectedCategory === 'all' || style.category.toLowerCase() === selectedCategory.toLowerCase();
  });

  const StyleCard = ({ style }: { style: LinkCardStyle }) => {
    const isSelected = selectedStyleId === style.id;
    
    return (
      <Card 
        className={`cursor-pointer transition-all hover:shadow-md ${
          isSelected ? 'ring-2 ring-purple-500 bg-purple-50' : ''
        }`}
        onClick={() => handleStyleSelect(style)}
      >
        <CardContent className="p-4">
          <div className="space-y-3">
            {/* Style Name and Category */}
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                {style.icon}
                <div>
                  <h3 className="font-semibold text-sm">{style.name}</h3>
                  <p className="text-xs text-muted-foreground">{style.category}</p>
                </div>
              </div>
              {isSelected && (
                <Badge variant="default" className="text-xs">
                  Selected
                </Badge>
              )}
            </div>

            {/* Style Preview */}
            <div className="relative">
              <div
                className={`p-3 text-center text-sm font-medium rounded-lg transition-all duration-300 ${style.cssClasses}`}
              >
                <span className={style.textColor}>{previewText}</span>
              </div>
            </div>

            {/* Style Description */}
            <p className="text-xs text-muted-foreground">{style.description}</p>
          </div>
        </CardContent>
      </Card>
    );
  };



  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Layout className="w-5 h-5 mr-2" />
          Link Card Styles
        </CardTitle>
        <CardDescription>
          Choose the perfect style for your link cards
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Category Filter */}
        <div className="flex gap-2 flex-wrap">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
          >
            All
          </Button>
          <Button
            variant={selectedCategory === 'modern' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('modern')}
          >
            Modern
          </Button>
          <Button
            variant={selectedCategory === 'bold' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('bold')}
          >
            Bold
          </Button>
          <Button
            variant={selectedCategory === 'simple' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('simple')}
          >
            Simple
          </Button>
        </div>

        {/* Style Grid */}
        <div className="grid gap-3 max-h-96 overflow-y-auto">
          {filteredStyles.map((style) => (
            <StyleCard key={style.id} style={style} />
          ))}
        </div>

        {/* Selected Style Info */}
        {selectedStyleId && (
          <div className="mt-4 p-3 bg-purple-50 rounded-lg">
            <p className="text-sm font-medium">Selected Style: {PREDEFINED_STYLES.find(s => s.id === selectedStyleId)?.name}</p>
            <p className="text-sm text-muted-foreground">
              Style applied to all link cards
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
