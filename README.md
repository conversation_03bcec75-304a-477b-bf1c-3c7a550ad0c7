# 🔗 LinkVibe

A modern, customizable link-in-bio platform built with Next.js, Appwrite, and shadcn/ui. Create beautiful, personalized bio pages with drag-and-drop functionality, custom themes, and comprehensive analytics.

## ✨ Features

- 🎨 **Custom Design Studio** - Full customization with fonts, colors, backgrounds, and themes
- 🔗 **Link Management** - Add, edit, and organize your links with drag-and-drop
- 📊 **Analytics Dashboard** - Track clicks, views, and engagement metrics
- 🎭 **Theme System** - Pre-built themes and custom theme creation
- 🔤 **Font Library** - Extensive font collection with Google Fonts integration
- 🌈 **Color Customization** - Advanced color picker with glassmorphism effects
- 📱 **Mobile Responsive** - Optimized for all devices
- 🔐 **Authentication** - Secure login with Appwrite and Google OAuth
- ⚡ **Fast & Modern** - Built with Next.js 15 and React 19

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **UI Components**: shadcn/ui + Radix UI
- **Styling**: Tailwind CSS
- **Backend**: Supabase (Database, Auth, Storage)
- **State Management**: Zustand
- **Drag & Drop**: @dnd-kit
- **Icons**: Lucide React
- **Deployment**: Vercel

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd linkvibe
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   Fill in your Supabase credentials in `.env.local`

4. **Set up Supabase**
   - Follow the detailed setup guide in `SUPABASE_SETUP.md`
   - Create your database and collections
   - Configure authentication

5. **Populate collections**
   ```bash
   npm run setup:all
   ```
   This will create default fonts and link card styles in your Appwrite collections

   Or run individually:
   ```bash
   npm run setup:fonts
   npm run setup:link-card-styles
   ```

6. **Run the development server**
   ```bash
   npm run dev
   ```

7. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
linkvibe/
├── app/                    # Next.js app directory
├── components/             # Reusable UI components
│   ├── ui/                # shadcn/ui components
│   ├── font-selector.tsx  # Font selection component
│   └── ...
├── lib/                   # Utility libraries
│   ├── supabase.ts       # Supabase configuration
│   ├── fonts-service.ts  # Font management service
│   └── ...
├── scripts/              # Setup and utility scripts
│   └── setup-fonts.ts   # Font collection setup
├── SUPABASE_SETUP.md     # Supabase setup guide
└── README.md
```

## 🎨 Font System

LinkVibe includes a comprehensive font management system:

- **System Fonts**: Fast-loading system fonts (Arial, Times, etc.)
- **Google Fonts**: Popular web fonts with automatic loading
- **Font Categories**: Organized by serif, sans-serif, monospace, cursive
- **Font Preview**: Real-time preview with custom text
- **Usage Tracking**: Analytics for font popularity

### Available Font Categories

- **Sans Serif**: Inter, Poppins, Arial, System Default
- **Serif**: Playfair Display, Times New Roman
- **Monospace**: Roboto Mono, Courier New
- **Cursive**: Dancing Script, Caveat

## 🎨 Link Card Styles System

LinkVibe includes a comprehensive link card styling system:

- **Dynamic Styles**: Load styles from Appwrite collection
- **Style Categories**: Glass, Neon, Minimal, Bold, Soft, Dark
- **Real-time Preview**: See styles applied instantly
- **Usage Analytics**: Track popular styles
- **Premium Styles**: Support for premium/paid styles

### Available Style Categories

- **🪟 Glass**: Elegant glassmorphism with transparency effects
- **⚡ Neon**: Vibrant cyberpunk-style glowing effects
- **📋 Minimal**: Clean and simple professional design
- **💪 Bold**: Strong gradients and impactful styling
- **🌸 Soft**: Gentle and subtle appearance
- **🌙 Dark**: Sleek dark theme design

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run setup:fonts` - Populate fonts collection
- `npm run setup:link-card-styles` - Populate link card styles collection
- `npm run setup:all` - Setup all collections (fonts + link card styles)

## 📚 Documentation

- [Appwrite Setup Guide](APPWRITE_SETUP.md) - Complete Appwrite configuration
- [Component Documentation](components/README.md) - UI component usage
- [API Documentation](lib/README.md) - Service layer documentation

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework
- [Supabase](https://supabase.com/) - Backend as a Service
- [shadcn/ui](https://ui.shadcn.com/) - UI component library
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
