'use client';

import React from 'react';

interface CinematicEffectsProps {
  enabled?: boolean;
  intensity?: 'low' | 'medium' | 'high';
  effect?: 'none' | 'motion-blur' | 'faded-sides' | 'aesthetic-vibe' | 'cinematic' | 'vintage' | 'dreamy';
  children: React.ReactNode;
}

export default function CinematicEffects({
  enabled = true,
  intensity = 'medium',
  effect = 'none',
  children
}: CinematicEffectsProps) {
  if (!enabled || effect === 'none') {
    return <>{children}</>;
  }

  const getEffectOverlays = () => {
    const baseIntensity = {
      low: { opacity: 0.4 },
      medium: { opacity: 0.6 },
      high: { opacity: 0.8 }
    }[intensity];

    switch (effect) {
      case 'motion-blur':
        return {
          overlay: `
            linear-gradient(
              90deg,
              rgba(0,0,0,${baseIntensity.opacity * 0.3}) 0%,
              transparent 20%,
              transparent 80%,
              rgba(0,0,0,${baseIntensity.opacity * 0.3}) 100%
            )
          `,
          animation: 'motionBlur 3s ease-in-out infinite alternate',
          mixBlendMode: 'multiply' as const
        };

      case 'faded-sides':
        return {
          overlay: `
            radial-gradient(
              ellipse at center,
              transparent 20%,
              rgba(0,0,0,${baseIntensity.opacity * 0.8}) 100%
            )
          `,
          mixBlendMode: 'multiply' as const
        };

      case 'aesthetic-vibe':
        return {
          overlay: `
            linear-gradient(
              135deg,
              rgba(255,182,193,${baseIntensity.opacity * 0.4}) 0%,
              rgba(255,218,185,${baseIntensity.opacity * 0.3}) 25%,
              rgba(186,225,255,${baseIntensity.opacity * 0.3}) 75%,
              rgba(221,160,221,${baseIntensity.opacity * 0.4}) 100%
            )
          `,
          mixBlendMode: 'soft-light' as const
        };

      case 'cinematic':
        return {
          overlay: `
            linear-gradient(
              135deg,
              rgba(0,0,0,${baseIntensity.opacity * 0.4}) 0%,
              rgba(0,0,0,${baseIntensity.opacity * 0.1}) 30%,
              rgba(0,0,0,${baseIntensity.opacity * 0.2}) 70%,
              rgba(0,0,0,${baseIntensity.opacity * 0.6}) 100%
            )
          `,
          mixBlendMode: 'multiply' as const,
          boxShadow: 'inset 0 0 100px rgba(0,0,0,0.3)'
        };

      case 'vintage':
        return {
          overlay: `
            radial-gradient(
              circle at center,
              rgba(139,69,19,${baseIntensity.opacity * 0.1}) 0%,
              rgba(160,82,45,${baseIntensity.opacity * 0.2}) 50%,
              rgba(101,67,33,${baseIntensity.opacity * 0.4}) 100%
            )
          `,
          mixBlendMode: 'multiply' as const
        };

      case 'dreamy':
        return {
          overlay: `
            radial-gradient(
              circle at 30% 70%,
              rgba(255,182,193,${baseIntensity.opacity * 0.3}) 0%,
              transparent 50%
            ),
            radial-gradient(
              circle at 70% 30%,
              rgba(173,216,230,${baseIntensity.opacity * 0.3}) 0%,
              transparent 50%
            ),
            linear-gradient(
              45deg,
              rgba(255,255,255,${baseIntensity.opacity * 0.1}) 0%,
              transparent 100%
            )
          `,
          animation: 'dreamyFloat 6s ease-in-out infinite alternate',
          mixBlendMode: 'soft-light' as const
        };

      default:
        return {};
    }
  };

  const effectOverlays = getEffectOverlays();

  return (
    <div className="relative w-full h-full">
      {/* Main content - no effects applied here */}
      {children}

      {/* Effect overlay - positioned on top with proper blend mode */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: effectOverlays.overlay,
          mixBlendMode: effectOverlays.mixBlendMode,
          boxShadow: effectOverlays.boxShadow,
          animation: effectOverlays.animation,
          zIndex: 2
        }}
      />

      {/* Additional particle effects for certain styles */}
      {(effect === 'aesthetic-vibe' || effect === 'dreamy') && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            backgroundImage: `
              radial-gradient(circle at 20% 80%, rgba(255,182,193,0.4) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(173,216,230,0.4) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(255,218,185,0.4) 0%, transparent 50%)
            `,
            animation: 'particleFloat 8s ease-in-out infinite alternate',
            zIndex: 2,
            opacity: 0.3
          }}
        />
      )}

      <style jsx>{`
        @keyframes motionBlur {
          0% {
            transform: translateX(-1px);
          }
          100% {
            transform: translateX(1px);
          }
        }

        @keyframes dreamyFloat {
          0% {
            transform: translateY(-1px) scale(1);
            opacity: 0.15;
          }
          100% {
            transform: translateY(1px) scale(1.005);
            opacity: 0.25;
          }
        }

        @keyframes particleFloat {
          0% {
            transform: translate(0, 0) scale(1);
            opacity: 0.1;
          }
          50% {
            transform: translate(-0.5px, 0.5px) scale(1.005);
            opacity: 0.15;
          }
          100% {
            transform: translate(0.5px, -0.5px) scale(0.995);
            opacity: 0.1;
          }
        }
      `}</style>
    </div>
  );
}
