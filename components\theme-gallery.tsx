'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useThemeStore } from '@/lib/stores/theme-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { Theme } from '@/lib/themes-service';
import { 
  Palette, 
  Search, 
  Heart, 
  Download, 
  Eye, 
  Trash2, 
  Edit,
  Plus,
  Sparkles,
  Users
} from 'lucide-react';

interface ThemeGalleryProps {
  onThemeSelect?: (theme: any) => void;
}

export function ThemeGallery({ onThemeSelect }: ThemeGalleryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const {
    userThemes,
    publicThemes,
    defaultThemes,
    isLoading,
    loadUserThemes,
    loadPublicThemes,
    loadDefaultThemes,
    applyTheme,
    deleteTheme
  } = useThemeStore();

  const { user } = useAuthStore();

  useEffect(() => {
    loadDefaultThemes();
    loadPublicThemes();
    if (user?.id) {
      loadUserThemes(user.id);
    }
  }, [loadDefaultThemes, loadPublicThemes, loadUserThemes, user?.id]);

  const handleApplyTheme = async (theme: Theme) => {
    try {
      if (theme.$id) {
        await applyTheme(theme.$id);
      }
      onThemeSelect?.(theme.design);
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Failed to apply theme:', error);
    }
  };

  const handleDeleteTheme = async (themeId: string) => {
    if (confirm('Are you sure you want to delete this theme?')) {
      try {
        await deleteTheme(themeId);
      } catch (error) {
        console.error('Failed to delete theme:', error);
      }
    }
  };

  const getThemePreview = (theme: Theme) => {
    const { background, linkCards } = theme.design;
    
    let bgClass = 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500';
    if (background.type === 'color') {
      bgClass = background.value;
    }

    const linkStyles = {
      glass: 'bg-white/20 backdrop-blur-lg border border-white/30',
      neon: 'bg-black border-2 border-cyan-400',
      minimal: 'bg-white border border-gray-200',
      bold: 'bg-gradient-to-r from-purple-500 to-pink-500',
      soft: 'bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-200',
      dark: 'bg-gray-900 border border-gray-700'
    };

    const linkClass = linkStyles[linkCards.style as keyof typeof linkStyles] || linkStyles.glass;

    return (
      <div className={`w-full h-32 rounded-lg overflow-hidden ${bgClass} p-3 flex flex-col justify-center space-y-2`}>
        <div className="w-8 h-8 bg-white/30 rounded-full mx-auto" />
        <div className="space-y-1">
          <div className={`h-3 rounded ${linkClass} mx-2`} />
          <div className={`h-3 rounded ${linkClass} mx-2`} />
          <div className={`h-3 rounded ${linkClass} mx-2`} />
        </div>
      </div>
    );
  };

  const filteredThemes = (themes: Theme[]) => {
    return themes.filter(theme => {
      const matchesSearch = theme.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           theme.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || theme.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Sparkles className="w-5 h-5 mr-2" />
            Theme Gallery
          </DialogTitle>
          <DialogDescription>
            Browse and apply beautiful themes for your bio page
          </DialogDescription>
        </DialogHeader>

        {/* Search and Filters */}
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search themes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Categories</option>
              <option value="minimal">Minimal</option>
              <option value="dark">Dark</option>
              <option value="neon">Neon</option>
              <option value="pastel">Pastel</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>

        {/* Theme Tabs */}
        <Tabs defaultValue="default" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="default">
              <Sparkles className="w-4 h-4 mr-2" />
              Default
            </TabsTrigger>
            <TabsTrigger value="public">
              <Users className="w-4 h-4 mr-2" />
              Community
            </TabsTrigger>
            <TabsTrigger value="my-themes">
              <Heart className="w-4 h-4 mr-2" />
              My Themes
            </TabsTrigger>
          </TabsList>

          {/* Default Themes */}
          <TabsContent value="default" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredThemes(defaultThemes).map((theme) => (
                <Card key={theme.$id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    {getThemePreview(theme)}
                  </CardContent>
                  <CardHeader className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-sm">{theme.name}</CardTitle>
                        <CardDescription className="text-xs">
                          {theme.description}
                        </CardDescription>
                        <div className="flex gap-1 mt-2">
                          {theme.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button
                        size="sm"
                        onClick={() => handleApplyTheme(theme)}
                        className="flex-1"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Apply
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Public Themes */}
          <TabsContent value="public" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredThemes(publicThemes).map((theme) => (
                <Card key={theme.$id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    {getThemePreview(theme)}
                  </CardContent>
                  <CardHeader className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-sm">{theme.name}</CardTitle>
                        <CardDescription className="text-xs">
                          {theme.description}
                        </CardDescription>
                        <div className="flex gap-1 mt-2">
                          {theme.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Used {theme.usageCount || 0} times
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button
                        size="sm"
                        onClick={() => handleApplyTheme(theme)}
                        className="flex-1"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Apply
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* User Themes */}
          <TabsContent value="my-themes" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredThemes(userThemes).map((theme) => (
                <Card key={theme.$id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    {getThemePreview(theme)}
                  </CardContent>
                  <CardHeader className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-sm">{theme.name}</CardTitle>
                        <CardDescription className="text-xs">
                          {theme.description}
                        </CardDescription>
                        <div className="flex gap-1 mt-2">
                          {theme.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button
                        size="sm"
                        onClick={() => handleApplyTheme(theme)}
                        className="flex-1"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Apply
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => theme.$id && handleDeleteTheme(theme.$id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
