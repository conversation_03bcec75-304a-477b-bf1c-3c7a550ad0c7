/**
 * <PERSON><PERSON><PERSON> to populate the fonts collection with default fonts and sync Google Fonts
 * Run this once after creating the fonts collection in Supabase
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') });

import { fontsService } from '../lib/fonts-service';

async function setupFonts() {
  console.log('🎨 Setting up fonts collection...');

  try {
    // Check if fonts already exist
    const existingFonts = await fontsService.getAllFonts(false); // Don't include Google Fonts API

    if (existingFonts.length > 0) {
      console.log(`ℹ️  Found ${existingFonts.length} existing fonts in database.`);
      console.log('Adding Google Fonts sync...');
    } else {
      // Create default fonts
      console.log('📝 Creating default fonts...');
      await fontsService.createDefaultFonts();
    }

    // Sync Google Fonts to database for caching
    console.log('🔄 Syncing Google Fonts to database...');
    await fontsService.syncGoogleFontsToDatabase(50); // Sync top 50 popular fonts

    // Verify creation
    const newFonts = await fontsService.getAllFonts(false);
    console.log(`✅ Successfully setup ${newFonts.length} fonts in database!`);

    // Show summary
    const system = newFonts.filter(f => f.is_system_font);
    const google = newFonts.filter(f => f.is_google_font);

    console.log('\n📊 Font Summary:');
    console.log(`   • Total fonts in database: ${newFonts.length}`);
    console.log(`   • System fonts: ${system.length}`);
    console.log(`   • Google fonts cached: ${google.length}`);
    console.log(`   • Additional Google Fonts available via API: 1000+`);

    console.log('\n🎉 Fonts setup complete!');
    console.log('You can now use the FontSelector component with Google Fonts integration.');

  } catch (error) {
    console.error('❌ Failed to setup fonts:', error);
    process.exit(1);
  }
}

// Run the setup
setupFonts();
