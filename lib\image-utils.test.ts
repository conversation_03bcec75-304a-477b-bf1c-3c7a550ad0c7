/**
 * Test file for image utilities
 * This is a simple test to verify the image validation works correctly
 */

import { validateImageFileSync, validateImageFile } from './image-utils';

// Mock File constructor for testing
class MockFile extends File {
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    super(bits, name, options);
  }
}

// Test synchronous validation
export function testSyncValidation() {
  console.log('🧪 Testing synchronous image validation...');

  // Test valid image file
  const validImageFile = new MockFile([''], 'test.jpg', { type: 'image/jpeg' });
  const validResult = validateImageFileSync(validImageFile);
  console.log('✅ Valid image file:', validResult);

  // Test invalid file type
  const invalidTypeFile = new MockFile([''], 'test.txt', { type: 'text/plain' });
  const invalidTypeResult = validateImageFileSync(invalidTypeFile);
  console.log('❌ Invalid file type:', invalidTypeResult);

  // Test file too large (simulate 11MB file)
  const largeFile = new MockFile(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
  const largeFileResult = validateImageFileSync(largeFile);
  console.log('❌ File too large:', largeFileResult);

  console.log('✅ Synchronous validation tests completed');
}

// Test async validation (would need actual image data to test properly)
export async function testAsyncValidation() {
  console.log('🧪 Testing asynchronous image validation...');

  // This would need actual image blob data to test properly
  // For now, just test the function exists and handles invalid types
  const invalidFile = new MockFile([''], 'test.txt', { type: 'text/plain' });
  
  try {
    const result = await validateImageFile(invalidFile);
    console.log('❌ Invalid file type (async):', result);
  } catch (error) {
    console.error('Error in async validation:', error);
  }

  console.log('✅ Asynchronous validation tests completed');
}

// Export test runner
export function runImageUtilsTests() {
  console.log('🚀 Running image utilities tests...');
  testSyncValidation();
  testAsyncValidation();
  console.log('🎉 All image utilities tests completed');
}
