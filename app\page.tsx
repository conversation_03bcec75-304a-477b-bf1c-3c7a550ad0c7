import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Palette,
  Smartphone,
  BarChart3,
  Zap,
  Users,
  Globe,
  Star,
  Check,
  ArrowRight,
  Sparkles
} from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      {/* Navigation */}
      <nav className="border-b bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                LinkVibe
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" className="hidden sm:inline-flex">
                Features
              </Button>
              <Button variant="ghost" className="hidden sm:inline-flex">
                Pricing
              </Button>
              <Link href="/auth">
                <Button variant="outline">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth">
                <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 sm:py-32">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/5 dark:to-pink-500/5" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Badge variant="secondary" className="mb-6 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
              ✨ The Future of Bio Link Pages
            </Badge>
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight text-slate-900 dark:text-white mb-6">
              Create Your Perfect{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Bio Link Page
              </span>{" "}
              in Minutes
            </h1>
            <p className="text-xl sm:text-2xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto mb-10 leading-relaxed">
              Build stunning, personalized landing pages with our intuitive drag-and-drop builder.
              No coding required – just pure creativity and professional results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/dashboard">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-lg px-8 py-6 h-auto"
                >
                  Start Building for Free
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6 h-auto">
                View Live Demo
              </Button>
            </div>
            <p className="text-sm text-slate-500 dark:text-slate-400 mt-4">
              No credit card required • 5-minute setup • Cancel anytime
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 sm:py-32 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-4">
              Everything You Need to{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Stand Out
              </span>
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Powerful features designed to help creators, entrepreneurs, and influencers
              build their perfect online presence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1: Drag & Drop Builder */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold">Drag & Drop Builder</CardTitle>
                <CardDescription className="text-base">
                  Intuitive interface that lets you create stunning pages in minutes.
                  No technical skills required.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 2: Beautiful Templates */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4">
                  <Palette className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold">Beautiful Templates</CardTitle>
                <CardDescription className="text-base">
                  Choose from professionally designed themes: Minimal, Dark Mode, Neon,
                  Pastel, and more.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 3: Mobile Optimized */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4">
                  <Smartphone className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold">Mobile Optimized</CardTitle>
                <CardDescription className="text-base">
                  Perfect on every device. Your bio page looks stunning on mobile,
                  tablet, and desktop.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 4: Analytics Dashboard */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center mb-4">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold">Analytics Dashboard</CardTitle>
                <CardDescription className="text-base">
                  Track visits, clicks, and engagement. Understand your audience
                  with detailed insights.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 5: Social Integration */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950/20 dark:to-purple-950/20">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold">Social Integration</CardTitle>
                <CardDescription className="text-base">
                  Connect all your social platforms. Instagram, TikTok, YouTube,
                  Twitter, and more.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 6: Custom Domains */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-teal-50 to-blue-50 dark:from-teal-950/20 dark:to-blue-950/20">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-500 rounded-lg flex items-center justify-center mb-4">
                  <Globe className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold">Custom Domains</CardTitle>
                <CardDescription className="text-base">
                  Use your own domain for a professional look. Build your brand
                  with custom URLs.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Templates Preview Section */}
      <section className="py-20 sm:py-32 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-4">
              Choose Your{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Perfect Style
              </span>
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              From minimal elegance to bold statements, find the template that matches your vibe.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Minimal Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden">
              <div className="aspect-[3/4] bg-gradient-to-b from-white to-gray-50 p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full mb-4"></div>
                <div className="w-24 h-3 bg-gray-300 rounded mb-2"></div>
                <div className="w-16 h-2 bg-gray-200 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gray-100 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-100 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-100 rounded-lg"></div>
                </div>
              </div>
              <CardHeader className="pt-4">
                <CardTitle className="text-center">Minimal</CardTitle>
                <CardDescription className="text-center">Clean and professional</CardDescription>
              </CardHeader>
            </Card>

            {/* Dark Mode Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden">
              <div className="aspect-[3/4] bg-gradient-to-b from-gray-900 to-black p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gray-700 rounded-full mb-4"></div>
                <div className="w-24 h-3 bg-gray-600 rounded mb-2"></div>
                <div className="w-16 h-2 bg-gray-700 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gray-800 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-800 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-800 rounded-lg"></div>
                </div>
              </div>
              <CardHeader className="pt-4">
                <CardTitle className="text-center">Dark Mode</CardTitle>
                <CardDescription className="text-center">Sleek and modern</CardDescription>
              </CardHeader>
            </Card>

            {/* Neon Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden">
              <div className="aspect-[3/4] bg-gradient-to-b from-purple-900 via-pink-900 to-black p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full mb-4 shadow-lg shadow-cyan-500/25"></div>
                <div className="w-24 h-3 bg-gradient-to-r from-cyan-400 to-pink-400 rounded mb-2"></div>
                <div className="w-16 h-2 bg-purple-400 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gradient-to-r from-cyan-500 to-pink-500 rounded-lg shadow-lg shadow-cyan-500/25"></div>
                  <div className="w-full h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg shadow-lg shadow-purple-500/25"></div>
                  <div className="w-full h-8 bg-gradient-to-r from-pink-500 to-orange-500 rounded-lg shadow-lg shadow-pink-500/25"></div>
                </div>
              </div>
              <CardHeader className="pt-4">
                <CardTitle className="text-center">Neon</CardTitle>
                <CardDescription className="text-center">Bold and vibrant</CardDescription>
              </CardHeader>
            </Card>

            {/* Pastel Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden">
              <div className="aspect-[3/4] bg-gradient-to-b from-pink-50 via-purple-50 to-blue-50 p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-200 to-purple-200 rounded-full mb-4"></div>
                <div className="w-24 h-3 bg-purple-200 rounded mb-2"></div>
                <div className="w-16 h-2 bg-pink-200 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gradient-to-r from-pink-200 to-purple-200 rounded-lg"></div>
                  <div className="w-full h-8 bg-gradient-to-r from-purple-200 to-blue-200 rounded-lg"></div>
                  <div className="w-full h-8 bg-gradient-to-r from-blue-200 to-cyan-200 rounded-lg"></div>
                </div>
              </div>
              <CardHeader className="pt-4">
                <CardTitle className="text-center">Pastel</CardTitle>
                <CardDescription className="text-center">Soft and dreamy</CardDescription>
              </CardHeader>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button size="lg" variant="outline" className="text-lg px-8 py-6 h-auto">
              View All Templates
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 sm:py-32 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-4">
              Simple,{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Transparent Pricing
              </span>
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Start for free, upgrade when you're ready. No hidden fees, no surprises.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Tier */}
            <Card className="border-2 border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-300 relative">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold">Free</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">$0</span>
                  <span className="text-slate-600 dark:text-slate-400">/month</span>
                </div>
                <CardDescription className="text-base mt-2">
                  Perfect for getting started
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>1 bio page</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>5 basic templates</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Basic analytics</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Mobile optimized</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>LinkVibe subdomain</span>
                  </div>
                </div>
                <Button className="w-full mt-8" variant="outline">
                  Get Started Free
                </Button>
              </CardContent>
            </Card>

            {/* Creator Tier */}
            <Card className="border-2 border-purple-300 dark:border-purple-600 hover:border-purple-400 dark:hover:border-purple-500 transition-all duration-300 relative shadow-lg shadow-purple-500/10">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1">
                  Most Popular
                </Badge>
              </div>
              <CardHeader className="text-center pb-8 pt-8">
                <CardTitle className="text-2xl font-bold">Creator</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">$9</span>
                  <span className="text-slate-600 dark:text-slate-400">/month</span>
                </div>
                <CardDescription className="text-base mt-2">
                  For serious creators and businesses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Unlimited bio pages</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>All premium templates</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Advanced analytics</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Custom domains</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Priority support</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span>Remove LinkVibe branding</span>
                  </div>
                </div>
                <Button className="w-full mt-8 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                  Start Free Trial
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <p className="text-slate-600 dark:text-slate-400">
              All plans include a 14-day free trial. No credit card required.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 sm:py-32 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Build Your Bio Link Page?
          </h2>
          <p className="text-xl text-purple-100 mb-10 max-w-2xl mx-auto">
            Join thousands of creators who are already using LinkVibe to showcase their work
            and grow their audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/dashboard">
              <Button
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-8 py-6 h-auto"
              >
                Start Building for Free
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </Link>
            <Button
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-6 h-auto"
            >
              View Live Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">LinkVibe</span>
              </div>
              <p className="text-slate-400 max-w-md">
                The easiest way to create beautiful, professional bio link pages.
                Build your online presence in minutes, not hours.
              </p>
            </div>

            {/* Product */}
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Templates</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Analytics</a></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Support</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-slate-700" />

          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">
              © 2024 LinkVibe. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-slate-400 hover:text-white transition-colors text-sm">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
