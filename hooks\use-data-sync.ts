import { useState, useCallback, useEffect } from 'react';
import { useUserStore } from '@/lib/stores/user-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { dataSyncService } from '@/lib/data-sync-service';

export interface DataSyncStatus {
  isSaving: boolean;
  isLoading: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  error: string | null;
}

export function useDataSync() {
  const { isAuthenticated } = useAuthStore();
  const { saveToAppwrite, loadFromAppwrite, autoSave } = useUserStore();
  
  const [status, setStatus] = useState<DataSyncStatus>({
    isSaving: false,
    isLoading: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    error: null
  });

  // Manual save function
  const saveData = useCallback(async () => {
    if (!isAuthenticated) {
      setStatus(prev => ({ ...prev, error: 'User not authenticated' }));
      return false;
    }

    try {
      setStatus(prev => ({ ...prev, isSaving: true, error: null }));
      
      const success = await saveToAppwrite();
      
      if (success) {
        setStatus(prev => ({
          ...prev,
          isSaving: false,
          lastSaved: new Date(),
          hasUnsavedChanges: false,
          error: null
        }));
        return true;
      } else {
        setStatus(prev => ({
          ...prev,
          isSaving: false,
          error: 'Failed to save data'
        }));
        return false;
      }
    } catch (error) {
      setStatus(prev => ({
        ...prev,
        isSaving: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }));
      return false;
    }
  }, [isAuthenticated, saveToAppwrite]);

  // Load data function
  const loadData = useCallback(async () => {
    if (!isAuthenticated) {
      setStatus(prev => ({ ...prev, error: 'User not authenticated' }));
      return false;
    }

    try {
      setStatus(prev => ({ ...prev, isLoading: true, error: null }));
      
      const success = await loadFromAppwrite();
      
      if (success) {
        setStatus(prev => ({
          ...prev,
          isLoading: false,
          hasUnsavedChanges: false,
          error: null
        }));
        return true;
      } else {
        setStatus(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to load data'
        }));
        return false;
      }
    } catch (error) {
      setStatus(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }));
      return false;
    }
  }, [isAuthenticated, loadFromAppwrite]);

  // Auto-save function
  const triggerAutoSave = useCallback(() => {
    if (isAuthenticated) {
      autoSave();
      setStatus(prev => ({ ...prev, hasUnsavedChanges: true }));
    }
  }, [isAuthenticated, autoSave]);

  // Export data function
  const exportData = useCallback(async () => {
    if (!isAuthenticated) {
      throw new Error('User not authenticated');
    }

    try {
      const { user } = useAuthStore.getState();
      if (!user) {
        throw new Error('User not found');
      }

      const jsonData = await dataSyncService.exportUserData(user.id);
      
      // Create and download file
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `linkvibe-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      return true;
    } catch (error) {
      console.error('Failed to export data:', error);
      throw error;
    }
  }, [isAuthenticated]);

  // Import data function
  const importData = useCallback(async (file: File) => {
    if (!isAuthenticated) {
      throw new Error('User not authenticated');
    }

    try {
      const { user } = useAuthStore.getState();
      const { setProfile, reorderLinks } = useUserStore.getState();

      if (!user) {
        throw new Error('User not found');
      }

      const jsonData = await file.text();
      await dataSyncService.importUserData(user.id, jsonData, setProfile, reorderLinks);

      // Reload the data to reflect changes
      await loadData();

      return true;
    } catch (error) {
      console.error('Failed to import data:', error);
      throw error;
    }
  }, [isAuthenticated, loadData]);

  // Clear error function
  const clearError = useCallback(() => {
    setStatus(prev => ({ ...prev, error: null }));
  }, []);

  // Note: Removed auto-load data on authentication
  // Data will only be loaded manually or when user explicitly requests it
  // This prevents automatic saving of default/mock data

  // Auto-save on window beforeunload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (status.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
        // Trigger a final save attempt
        saveData();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [status.hasUnsavedChanges, saveData]);

  return {
    // Status
    status,
    
    // Actions
    saveData,
    loadData,
    triggerAutoSave,
    exportData,
    importData,
    clearError,
    
    // Convenience flags
    canSave: isAuthenticated && !status.isSaving,
    canLoad: isAuthenticated && !status.isLoading,
    isWorking: status.isSaving || status.isLoading,
  };
}

// Note: useAutoSave hook removed - using manual save buttons instead

// Hook for saving specific profile changes
export function useSaveProfile() {
  const { saveData, status } = useDataSync();
  
  const saveProfile = useCallback(async (profileUpdates: any) => {
    const { updateProfile } = useUserStore.getState();
    
    // Update the store first
    updateProfile(profileUpdates);
    
    // Then save to Appwrite
    return await saveData();
  }, [saveData]);

  return {
    saveProfile,
    isSaving: status.isSaving,
    error: status.error
  };
}

// Hook for saving link changes
export function useSaveLinks() {
  const { saveData, status } = useDataSync();
  
  const saveLinks = useCallback(async () => {
    return await saveData();
  }, [saveData]);

  return {
    saveLinks,
    isSaving: status.isSaving,
    error: status.error
  };
}
