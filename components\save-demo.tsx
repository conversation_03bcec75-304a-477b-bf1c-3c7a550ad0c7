'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useUserStore } from '@/lib/stores/user-store';
import { useDataSync, useSaveProfile } from '@/hooks/use-data-sync';
import { SaveButton, AutoSaveIndicator } from '@/components/save-button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  Upload, 
  Palette, 
  Image, 
  Type,
  Sparkles,
  Link as LinkIcon
} from 'lucide-react';

export function SaveDemo() {
  const { profile, updateProfile, addLink, updateLink } = useUserStore();
  const { saveData, loadData, exportData, importData, status } = useDataSync();
  const { saveProfile } = useSaveProfile();
  const [fileInput, setFileInput] = useState<HTMLInputElement | null>(null);

  // Example: Save profile changes
  const handleProfileChange = async (field: string, value: any) => {
    const success = await saveProfile({ [field]: value });
    if (success) {
      console.log('Profile saved successfully!');
    }
  };

  // Example: Save custom colors
  const handleColorChange = async (colors: any) => {
    const success = await saveProfile({ 
      customColors: colors,
      template: 'custom' 
    });
    if (success) {
      console.log('Colors saved successfully!');
    }
  };

  // Example: Save background settings
  const handleBackgroundChange = async (backgroundData: any) => {
    const success = await saveProfile(backgroundData);
    if (success) {
      console.log('Background saved successfully!');
    }
  };

  // Example: Save link styling
  const handleLinkStyleChange = async (linkId: string, styling: any) => {
    updateLink(linkId, styling);
    const success = await saveData();
    if (success) {
      console.log('Link styling saved successfully!');
    }
  };

  // Export data
  const handleExport = async () => {
    try {
      await exportData();
      console.log('Data exported successfully!');
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Import data
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await importData(file);
        console.log('Data imported successfully!');
      } catch (error) {
        console.error('Import failed:', error);
      }
    }
  };

  if (!profile) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Save Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Save Status & Controls
          </CardTitle>
          <CardDescription>
            Monitor and control your data synchronization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <AutoSaveIndicator />
            <div className="flex gap-2">
              <SaveButton size="sm" />
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadData}
                disabled={status.isLoading}
              >
                Reload
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Badge variant={status.hasUnsavedChanges ? "destructive" : "secondary"}>
                {status.hasUnsavedChanges ? "Unsaved Changes" : "All Saved"}
              </Badge>
            </div>
            <div>
              {status.lastSaved && (
                <span className="text-muted-foreground">
                  Last saved: {status.lastSaved.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>

          {status.error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-800 text-sm">
              Error: {status.error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Profile Customization Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Profile Customization
          </CardTitle>
          <CardDescription>
            Examples of saving different profile settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Basic Profile Info */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Display Name</label>
            <Input
              value={profile.name}
              onChange={(e) => handleProfileChange('name', e.target.value)}
              placeholder="Your display name"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Bio</label>
            <Textarea
              value={profile.bio}
              onChange={(e) => handleProfileChange('bio', e.target.value)}
              placeholder="Tell people about yourself..."
              rows={3}
            />
          </div>

          <Separator />

          {/* Color Customization */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Custom Colors</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs text-muted-foreground">Primary Color</label>
                <input
                  type="color"
                  value={profile.customColors?.primary || '#6366f1'}
                  onChange={(e) => handleColorChange({
                    ...profile.customColors,
                    primary: e.target.value
                  })}
                  className="w-full h-10 rounded border"
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground">Background Color</label>
                <input
                  type="color"
                  value={profile.customColors?.background || '#ffffff'}
                  onChange={(e) => handleColorChange({
                    ...profile.customColors,
                    background: e.target.value
                  })}
                  className="w-full h-10 rounded border"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Background Settings */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Background Type</label>
            <div className="grid grid-cols-4 gap-2">
              {['color', 'image', 'gradient', 'none'].map((type) => (
                <Button
                  key={type}
                  variant={profile.backgroundType === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleBackgroundChange({ backgroundType: type })}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          {/* Font Settings */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Font Size</label>
            <div className="grid grid-cols-3 gap-2">
              {['small', 'medium', 'large'].map((size) => (
                <Button
                  key={size}
                  variant={profile.fontSize === size ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleProfileChange('fontSize', size)}
                >
                  {size}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Data Management
          </CardTitle>
          <CardDescription>
            Export and import your LinkVibe data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={handleExport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            
            <Button 
              onClick={() => fileInput?.click()} 
              variant="outline"
            >
              <Upload className="h-4 w-4 mr-2" />
              Import Data
            </Button>
            
            <input
              ref={setFileInput}
              type="file"
              accept=".json"
              onChange={handleImport}
              className="hidden"
            />
          </div>
          
          <p className="text-sm text-muted-foreground">
            Export your data for backup or import to restore from a backup file.
          </p>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use the Save System</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <strong>Auto-Save:</strong> Changes are automatically saved after 2 seconds of inactivity.
          </div>
          <div>
            <strong>Manual Save:</strong> Click the save button or press Ctrl+S (Cmd+S on Mac).
          </div>
          <div>
            <strong>Status Indicators:</strong> Watch the save status in the header and status bar.
          </div>
          <div>
            <strong>Data Sync:</strong> All profile details, links, colors, and styling are saved to Appwrite.
          </div>
          <div>
            <strong>Backup:</strong> Export your data regularly for backup purposes.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
