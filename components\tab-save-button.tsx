'use client';

import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { useDataSync } from '@/hooks/use-data-sync';
import { useUserStore } from '@/lib/stores/user-store';
import { useProfileStore } from '@/lib/stores/profile-store';
import { useLinksStore } from '@/lib/stores/links-store';
import { useThemeStore } from '@/lib/stores/theme-store';
import { useAppStore } from '@/lib/stores/app-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { dataSyncService } from '@/lib/data-sync-service';
import { Save, Check, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TabSaveButtonProps {
  tabType: 'profile' | 'design' | 'analytics' | 'links';
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
}

export function TabSaveButton({
  tabType,
  variant = 'default',
  size = 'md',
  className,
  disabled = false
}: TabSaveButtonProps) {
  const { user } = useAuthStore();
  const profileStore = useProfileStore();
  const linksStore = useLinksStore();
  const { addNotification } = useAppStore();
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [saveCount, setSaveCount] = useState(0);

  const handleSave = useCallback(async () => {
    // Prevent duplicate saves
    if (isSaving || disabled || !user?.id) return;

    // Prevent rapid successive saves
    const now = Date.now();
    const lastSaveTime = lastSaved?.getTime() || 0;
    if (now - lastSaveTime < 1000) { // 1 second cooldown
      return;
    }

    try {
      setIsSaving(true);
      setError(null);

      let success = false;

      if (tabType === 'profile') {
        // Save profile data using the new ProfileStore and DataSyncService
        const profile = profileStore.profile;
        if (profile) {
          await dataSyncService.saveProfileData(user.id, profile);
          profileStore.markUnsavedChanges(false);
          success = true;
        }
      } else if (tabType === 'links') {
        // Save links data using LinksStore
        await linksStore.saveAllLinks(user.id);
        success = true;
      } else if (tabType === 'design') {
        // Save design data using ProfileStore
        const profile = profileStore.profile;
        if (profile) {
          // Get current design from theme store
          let { currentTheme } = useThemeStore.getState();

          // Check if background image needs to be uploaded to storage
          console.log('🔍 Checking background image:', currentTheme?.background);
          if (currentTheme?.background?.type === 'image' &&
              currentTheme?.background?.value?.includes('blob:')) {

            // Get the background image file from user store
            const { profile: userProfile } = useUserStore.getState();
            if (userProfile?.backgroundImageFile) {
              try {
                console.log('📤 Uploading background image during save...');
                const { handleImageUploadToStorage } = await import('@/lib/image-utils');

                // Upload to Supabase storage
                await new Promise<void>((resolve, reject) => {
                  handleImageUploadToStorage(
                    userProfile.backgroundImageFile!,
                    user.id,
                    'background',
                    (permanentUrl, fileId) => {
                      // Update the design with permanent URL
                      currentTheme = {
                        ...currentTheme,
                        background: {
                          ...currentTheme.background,
                          value: `url(${permanentUrl})`
                        }
                      };
                      console.log('✅ Background image uploaded, updated design with permanent URL');
                      resolve();
                    },
                    (error) => {
                      console.error('❌ Failed to upload background during save:', error);
                      reject(new Error(error));
                    }
                  );
                });
              } catch (uploadError) {
                console.error('❌ Background upload failed during save:', uploadError);
                // Continue with save but show warning
                throw new Error('Failed to upload background image');
              }
            }
          }

          // Save the design (with permanent URL if upload was successful)
          await profileStore.updateProfile({ design: currentTheme });
          success = true;
        }
      } else {
        // For other tabs, use the old save method for now
        // TODO: Update analytics saving to use new stores
        const { saveToAppwrite } = useUserStore.getState();
        success = await saveToAppwrite();
      }

      if (success) {
        const saveTime = new Date();
        setLastSaved(saveTime);
        setSaveCount(prev => prev + 1);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 2000);

        // Show success notification
        addNotification({
          type: 'success',
          title: `${getTabLabel()} Saved`,
          message: `Your ${getTabLabel().toLowerCase()} changes have been saved to Supabase successfully!`,
          duration: 3000
        });

        // Reload data after successful save
        if (tabType === 'profile') {
          try {
            await profileStore.loadProfile(user.id, true);
            console.log('✅ Profile data reloaded after save');
          } catch (loadError) {
            console.warn('⚠️ Failed to reload profile after save:', loadError);
          }
        } else if (tabType === 'links') {
          try {
            await linksStore.loadLinks(user.id, true);
            console.log('✅ Links data reloaded after save');
          } catch (loadError) {
            console.warn('⚠️ Failed to reload links after save:', loadError);
          }
        } else if (tabType === 'design') {
          try {
            await profileStore.loadProfile(user.id, true);
            console.log('✅ Design data reloaded after save');
          } catch (loadError) {
            console.warn('⚠️ Failed to reload design after save:', loadError);
          }
        }
      } else {
        setError('Failed to save changes');

        // Show error notification
        addNotification({
          type: 'error',
          title: `${getTabLabel()} Save Failed`,
          message: 'Failed to save changes to Supabase. Please try again.',
          duration: 5000
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);

      // Show error notification
      addNotification({
        type: 'error',
        title: `${getTabLabel()} Save Error`,
        message: `Error saving ${getTabLabel().toLowerCase()}: ${errorMessage}`,
        duration: 5000
      });
    } finally {
      setIsSaving(false);
    }
  }, [isSaving, disabled, user?.id, lastSaved, addNotification, tabType, profileStore]);

  const getIcon = () => {
    if (isSaving) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (showSuccess) {
      return <Check className="h-4 w-4" />;
    }
    
    if (error) {
      return <AlertCircle className="h-4 w-4" />;
    }
    
    return <Save className="h-4 w-4" />;
  };

  const getText = () => {
    if (isSaving) {
      return 'Saving...';
    }
    
    if (showSuccess) {
      return 'Saved!';
    }
    
    if (error) {
      return 'Error';
    }
    
    return 'Save Changes';
  };

  const getVariant = () => {
    if (showSuccess) {
      return 'default';
    }
    
    if (error) {
      return 'destructive';
    }
    
    return variant;
  };

  const getTabLabel = () => {
    switch (tabType) {
      case 'profile':
        return 'Profile';
      case 'design':
        return 'Design';
      case 'analytics':
        return 'Analytics';
      case 'links':
        return 'Links';
      default:
        return 'Changes';
    }
  };

  return (
    <Button
      onClick={handleSave}
      disabled={isSaving || disabled}
      variant={getVariant() as any}
      size={size}
      className={cn(
        'transition-all duration-200',
        showSuccess && 'bg-green-600 hover:bg-green-700 text-white',
        error && 'bg-red-600 hover:bg-red-700 text-white',
        className
      )}
      title={
        isSaving ? `Saving ${getTabLabel().toLowerCase()} changes...` :
        showSuccess ? `${getTabLabel()} changes saved successfully! (Save #${saveCount})` :
        error ? `Error saving ${getTabLabel().toLowerCase()}: ${error}` :
        lastSaved ? `Last saved: ${lastSaved.toLocaleTimeString()} (Save #${saveCount})` :
        `Save ${getTabLabel().toLowerCase()} changes`
      }
    >
      {getIcon()}
      <span className="ml-2">{getText()}</span>
    </Button>
  );
}

// Hook for tracking unsaved changes per tab
export function useTabSaveState(tabType: 'profile' | 'design' | 'analytics') {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const markAsChanged = useCallback(() => {
    setHasUnsavedChanges(true);
  }, []);

  const markAsSaved = useCallback(() => {
    setHasUnsavedChanges(false);
    setLastSaved(new Date());
  }, []);

  const resetState = useCallback(() => {
    setHasUnsavedChanges(false);
    setLastSaved(null);
  }, []);

  return {
    hasUnsavedChanges,
    lastSaved,
    markAsChanged,
    markAsSaved,
    resetState
  };
}
