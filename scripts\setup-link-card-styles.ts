/**
 * <PERSON><PERSON><PERSON> to populate the link card styles collection with default styles
 * Run this once after creating the linkCardStyles collection in Appwrite
 */

import { linkCardStylesService } from '../lib/link-card-styles-service';

async function setupLinkCardStyles() {
  console.log('🎨 Setting up link card styles collection...');
  
  try {
    // Check if styles already exist
    const existingStyles = await linkCardStylesService.getAllStyles();
    
    if (existingStyles.length > 0) {
      console.log(`ℹ️  Found ${existingStyles.length} existing styles. Skipping setup.`);
      console.log('If you want to reset styles, delete them from Appwrite console first.');
      return;
    }

    // Create default styles
    console.log('📝 Creating default link card styles...');
    await linkCardStylesService.createDefaultStyles();
    
    // Verify creation
    const newStyles = await linkCardStylesService.getAllStyles();
    console.log(`✅ Successfully created ${newStyles.length} link card styles!`);
    
    // Show summary
    const popular = newStyles.filter(s => s.isPopular);
    const defaults = newStyles.filter(s => s.isDefault);
    const premium = newStyles.filter(s => s.isPremium);
    const categories = [...new Set(newStyles.map(s => s.category))];
    
    console.log('\n📊 Link Card Styles Summary:');
    console.log(`   • Total styles: ${newStyles.length}`);
    console.log(`   • Popular styles: ${popular.length}`);
    console.log(`   • Default styles: ${defaults.length}`);
    console.log(`   • Premium styles: ${premium.length}`);
    console.log(`   • Categories: ${categories.join(', ')}`);
    
    console.log('\n🎨 Available Styles:');
    newStyles.forEach(style => {
      const badges = [];
      if (style.isPopular) badges.push('Popular');
      if (style.isDefault) badges.push('Default');
      if (style.isPremium) badges.push('Premium');
      
      console.log(`   • ${style.name} (${style.category})${badges.length ? ` - ${badges.join(', ')}` : ''}`);
    });
    
    console.log('\n🎉 Link card styles setup complete!');
    console.log('You can now use the LinkCardStyleSelector component in your app.');
    
  } catch (error) {
    console.error('❌ Failed to setup link card styles:', error);
    process.exit(1);
  }
}

// Run the setup
setupLinkCardStyles();
