import { supabase } from './supabase';
import { TABLES } from './supabase';

export interface LinkData {
  id?: string;
  user_id: string; // Supabase auth user ID
  title: string;
  url: string;
  description?: string;
  icon?: string;
  background_color?: string; // Individual link background color
  text_color?: string; // Individual link text color
  border_color?: string; // Individual link border color
  custom_style?: string; // Custom CSS for this specific link
  clicks: number;
  is_active: boolean;
  order: number;
  created_at?: string;
  updated_at?: string;
}

export interface SocialLinkData {
  id?: string;
  user_id: string;
  platform: string;
  url: string;
  is_visible: boolean;
  order: number;
  created_at?: string;
  updated_at?: string;
}

export class LinksService {
  // Create a new link
  async createLink(userId: string, linkData: Omit<LinkData, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'clicks'>): Promise<LinkData> {
    try {
      const data = {
        user_id: userId,
        ...linkData,
        clicks: 0
      };

      const { data: result, error } = await supabase
        .from(TABLES.LINKS)
        .insert(data)
        .select()
        .single();

      if (error) throw error;
      return result as LinkData;
    } catch (error) {
      console.error('Failed to create link:', error);
      throw error;
    }
  }

  // Get all links for a user
  async getUserLinks(userId: string): Promise<LinkData[]> {
    try {
      console.log('🔍 Querying links for userId:', userId);

      const { data, error } = await supabase
        .from(TABLES.LINKS)
        .select('*')
        .eq('user_id', userId)
        .order('order', { ascending: true });

      if (error) {
        // Check if it's a "table doesn't exist" or "no rows" error
        if (error.code === 'PGRST116' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('📝 Links table not found or empty - user will start with no links');
          return [];
        }
        throw error;
      }

      const linkCount = data?.length || 0;
      console.log(`📊 Found ${linkCount} links for user`);

      if (linkCount === 0) {
        console.log('📝 No links found - user will start with empty links');
      }

      return data as LinkData[] || [];
    } catch (error) {
      console.log('⚠️ Could not load user links (this is normal for new users):', error.message || error);
      return [];
    }
  }

  // Update a link
  async updateLink(linkId: string, updates: Partial<LinkData>): Promise<LinkData> {
    try {
      const updateData = { ...updates };

      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.user_id;
      delete updateData.created_at;
      delete updateData.updated_at;

      const { data, error } = await supabase
        .from(TABLES.LINKS)
        .update(updateData)
        .eq('id', linkId)
        .select()
        .single();

      if (error) throw error;
      return data as LinkData;
    } catch (error) {
      console.error('Failed to update link:', error);
      throw error;
    }
  }

  // Delete a link
  async deleteLink(linkId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.LINKS)
        .delete()
        .eq('id', linkId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to delete link:', error);
      throw error;
    }
  }

  // Reorder links
  async reorderLinks(userId: string, linkIds: string[]): Promise<void> {
    try {
      const updatePromises = linkIds.map((linkId, index) =>
        this.updateLink(linkId, { order: index })
      );

      await Promise.all(updatePromises);
    } catch (error) {
      console.error('Failed to reorder links:', error);
      throw error;
    }
  }

  // Increment link clicks
  async incrementLinkClicks(linkId: string): Promise<void> {
    try {
      const { data: link, error } = await supabase
        .from(TABLES.LINKS)
        .select('clicks')
        .eq('id', linkId)
        .single();

      if (error) throw error;

      await this.updateLink(linkId, {
        clicks: (link.clicks || 0) + 1
      });
    } catch (error) {
      console.error('Failed to increment link clicks:', error);
      // Don't throw error for analytics tracking
    }
  }

  // Bulk sync links (for saving all links at once)
  async syncUserLinks(userId: string, links: Omit<LinkData, 'id' | 'user_id' | 'created_at' | 'updated_at'>[]): Promise<LinkData[]> {
    try {
      // Get existing links
      const existingLinks = await this.getUserLinks(userId);
      const existingLinkIds = new Set(existingLinks.map(link => link.id));

      const results: LinkData[] = [];

      // Update or create links
      for (const linkData of links) {
        if (linkData.id && existingLinkIds.has(linkData.id)) {
          // Update existing link
          const updated = await this.updateLink(linkData.id, linkData);
          results.push(updated);
          existingLinkIds.delete(linkData.id);
        } else {
          // Create new link
          const created = await this.createLink(userId, linkData);
          results.push(created);
        }
      }

      // Delete links that are no longer present
      for (const linkId of existingLinkIds) {
        await this.deleteLink(linkId);
      }

      return results;
    } catch (error) {
      console.error('Failed to sync user links:', error);
      throw error;
    }
  }

  // Get public links for a user (for public profile view)
  async getPublicUserLinks(userId: string): Promise<LinkData[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.LINKS)
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('order', { ascending: true })
        .limit(50);

      if (error) throw error;
      return data as LinkData[] || [];
    } catch (error) {
      console.error('Failed to get public user links:', error);
      return [];
    }
  }

  // Search links
  async searchUserLinks(userId: string, query: string): Promise<LinkData[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.LINKS)
        .select('*')
        .eq('user_id', userId)
        .ilike('title', `%${query}%`)
        .limit(20);

      if (error) throw error;
      return data as LinkData[] || [];
    } catch (error) {
      console.error('Failed to search user links:', error);
      return [];
    }
  }

  // Get link analytics
  async getLinkAnalytics(userId: string, days: number = 30): Promise<any> {
    try {
      const links = await this.getUserLinks(userId);
      
      // Calculate total clicks
      const totalClicks = links.reduce((sum, link) => sum + (link.clicks || 0), 0);
      
      // Get most clicked links
      const topLinks = links
        .filter(link => link.clicks > 0)
        .sort((a, b) => (b.clicks || 0) - (a.clicks || 0))
        .slice(0, 5);

      return {
        totalClicks,
        totalLinks: links.length,
        activeLinks: links.filter(link => link.is_active).length,
        topLinks: topLinks.map(link => ({
          title: link.title,
          clicks: link.clicks,
          url: link.url
        }))
      };
    } catch (error) {
      console.error('Failed to get link analytics:', error);
      return {
        totalClicks: 0,
        totalLinks: 0,
        activeLinks: 0,
        topLinks: []
      };
    }
  }
}

// Export singleton instance
export const linksService = new LinksService();
