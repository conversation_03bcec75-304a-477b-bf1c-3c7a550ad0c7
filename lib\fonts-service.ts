import { supabase } from './supabase';
import { TABLES } from './supabase';
import { googleFontsAPI, GoogleFont } from './google-fonts-api';

export interface FontVariant {
  weight: string; // '400', '700', etc.
  style: string;  // 'normal', 'italic'
}

export interface Font {
  id?: string;
  name: string;
  display_name: string;
  category: 'serif' | 'sans-serif' | 'monospace' | 'cursive' | 'fantasy';
  variants: FontVariant[];
  subsets: string[];
  tags: string[];
  css_import_url?: string;
  css_font_family: string;
  is_google_font: boolean;
  is_system_font: boolean;
  is_premium: boolean;
  usage_count: number;
  google_font_data?: GoogleFont;
  created_at?: string;
  updated_at?: string;
}

export class FontsService {
  // Create a new font
  async createFont(fontData: Omit<Font, 'id' | 'created_at' | 'updated_at' | 'usage_count'>): Promise<Font> {
    try {
      const data = {
        ...fontData,
        usage_count: 0
      };

      const { data: result, error } = await supabase
        .from(TABLES.FONTS)
        .insert(data)
        .select()
        .single();

      if (error) throw error;
      return result as Font;
    } catch (error) {
      console.error('Failed to create font:', error);
      throw error;
    }
  }

  // Get all fonts (combines database fonts with Google Fonts)
  async getAllFonts(includeGoogleFonts: boolean = true): Promise<Font[]> {
    try {
      // Get fonts from database
      const { data: dbFonts, error } = await supabase
        .from(TABLES.FONTS)
        .select('*')
        .order('is_google_font', { ascending: false })
        .order('name', { ascending: true });

      if (error) throw error;

      let allFonts = (dbFonts as Font[]) || [];

      // If requested, also fetch popular Google Fonts
      if (includeGoogleFonts) {
        try {
          const googleFonts = await this.getGoogleFonts(50); // Get top 50 popular fonts
          allFonts = [...allFonts, ...googleFonts];
        } catch (googleError) {
          console.warn('Failed to fetch Google Fonts, using database fonts only:', googleError);
        }
      }

      return allFonts;
    } catch (error) {
      console.error('Failed to get fonts:', error);
      return [];
    }
  }

  // Get fonts by category
  async getFontsByCategory(category: string): Promise<Font[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.FONTS)
        .select('*')
        .eq('category', category)
        .order('is_google_font', { ascending: false })
        .order('name', { ascending: true })
        .limit(50);

      if (error) throw error;
      return data as Font[] || [];
    } catch (error) {
      console.error('Failed to get fonts by category:', error);
      return [];
    }
  }

  // Get popular fonts
  async getPopularFonts(limit: number = 20): Promise<Font[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.FONTS)
        .select('*')
        .eq('is_google_font', true)
        .order('usage_count', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data as Font[] || [];
    } catch (error) {
      console.error('Failed to get popular fonts:', error);
      return [];
    }
  }

  // Get default system fonts
  async getDefaultFonts(): Promise<Font[]> {
    try {
      // First try to get from database
      const { data, error } = await supabase
        .from(TABLES.FONTS)
        .select('*')
        .eq('is_system_font', true)
        .order('name', { ascending: true });

      if (error) throw error;

      // If we have data from database, return it
      if (data && data.length > 0) {
        return data as Font[];
      }

      // Fallback to hardcoded system fonts if database is empty
      return this.getHardcodedSystemFonts();
    } catch (error) {
      console.error('Failed to get default fonts from database, using fallback:', error);
      return this.getHardcodedSystemFonts();
    }
  }

  // Hardcoded system fonts as fallback
  private getHardcodedSystemFonts(): Font[] {
    return [
      {
        name: 'system-ui',
        display_name: 'System Default',
        category: 'sans-serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['system', 'safe', 'fast'],
        css_font_family: 'system-ui, -apple-system, sans-serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false,
        usage_count: 0
      },
      {
        name: 'arial',
        display_name: 'Arial',
        category: 'sans-serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['classic', 'readable', 'safe'],
        css_font_family: 'Arial, sans-serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false,
        usage_count: 0
      },
      {
        name: 'helvetica',
        display_name: 'Helvetica',
        category: 'sans-serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['classic', 'clean', 'modern'],
        css_font_family: 'Helvetica, Arial, sans-serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false,
        usage_count: 0
      },
      {
        name: 'times',
        display_name: 'Times New Roman',
        category: 'serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['classic', 'formal', 'traditional'],
        css_font_family: 'Times, "Times New Roman", serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false,
        usage_count: 0
      },
      {
        name: 'georgia',
        display_name: 'Georgia',
        category: 'serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['readable', 'elegant', 'web-safe'],
        css_font_family: 'Georgia, serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false,
        usage_count: 0
      },
      {
        name: 'courier',
        display_name: 'Courier New',
        category: 'monospace',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['monospace', 'code', 'technical'],
        css_font_family: 'Courier, "Courier New", monospace',
        is_google_font: false,
        is_system_font: true,
        is_premium: false,
        usage_count: 0
      }
    ];
  }

  // Search fonts
  async searchFonts(query: string): Promise<Font[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.FONTS)
        .select('*')
        .or(`name.ilike.%${query}%,display_name.ilike.%${query}%`)
        .limit(20);

      if (error) throw error;
      return data as Font[] || [];
    } catch (error) {
      console.error('Failed to search fonts:', error);
      return [];
    }
  }

  // Increment font usage
  async incrementUsage(fontId: string): Promise<void> {
    try {
      const { data: font, error: fetchError } = await supabase
        .from(TABLES.FONTS)
        .select('usage_count')
        .eq('id', fontId)
        .single();

      if (fetchError) throw fetchError;

      const { error: updateError } = await supabase
        .from(TABLES.FONTS)
        .update({ usage_count: (font.usage_count || 0) + 1 })
        .eq('id', fontId);

      if (updateError) throw updateError;
    } catch (error) {
      console.error('Failed to increment font usage:', error);
      // Don't throw error for usage tracking
    }
  }

  // Get Google Fonts from API
  async getGoogleFonts(limit: number = 50): Promise<Font[]> {
    try {
      const googleFonts = await googleFontsAPI.getPopularFonts(limit);
      return googleFonts.map(gFont => googleFontsAPI.convertToInternalFormat(gFont));
    } catch (error) {
      console.error('Failed to fetch Google Fonts:', error);
      return [];
    }
  }

  // Search Google Fonts
  async searchGoogleFonts(query: string): Promise<Font[]> {
    try {
      const googleFonts = await googleFontsAPI.searchFonts(query);
      return googleFonts.map(gFont => googleFontsAPI.convertToInternalFormat(gFont));
    } catch (error) {
      console.error('Failed to search Google Fonts:', error);
      return [];
    }
  }

  // Get Google Fonts by category
  async getGoogleFontsByCategory(category: string): Promise<Font[]> {
    try {
      const googleFonts = await googleFontsAPI.getFontsByCategory(category);
      return googleFonts.map(gFont => googleFontsAPI.convertToInternalFormat(gFont));
    } catch (error) {
      console.error('Failed to get Google Fonts by category:', error);
      return [];
    }
  }

  // Load Google Font dynamically
  loadGoogleFont(googleFontName: string, variants: FontVariant[] = []): void {
    if (typeof window === 'undefined') return;

    // Use the Google Fonts API service to load the font
    const variantStrings = variants.map(v => v.weight);
    googleFontsAPI.loadFont(googleFontName, variantStrings);
  }

  // Sync popular Google Fonts to database (for caching)
  async syncGoogleFontsToDatabase(limit: number = 100): Promise<void> {
    try {
      console.log('🔄 Syncing Google Fonts to database...');
      const googleFonts = await googleFontsAPI.getPopularFonts(limit);

      for (const gFont of googleFonts) {
        try {
          // Check if font already exists
          const { data: existing } = await supabase
            .from(TABLES.FONTS)
            .select('id')
            .eq('name', gFont.family)
            .eq('is_google_font', true)
            .single();

          if (!existing) {
            // Convert and create font
            const fontData = googleFontsAPI.convertToInternalFormat(gFont);
            await this.createFont(fontData);
            console.log(`✅ Synced font: ${gFont.family}`);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to sync font ${gFont.family}:`, error);
        }
      }

      console.log('✅ Google Fonts sync completed');
    } catch (error) {
      console.error('❌ Failed to sync Google Fonts:', error);
    }
  }

  // Create default fonts (run once during setup)
  async createDefaultFonts(): Promise<void> {
    const defaultFonts: Omit<Font, 'id' | 'created_at' | 'updated_at' | 'usage_count'>[] = [
      // System fonts
      {
        name: 'system-ui',
        display_name: 'System Default',
        category: 'sans-serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['system', 'safe', 'fast'],
        css_font_family: 'system-ui, -apple-system, sans-serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false
      },
      {
        name: 'arial',
        display_name: 'Arial',
        category: 'sans-serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['classic', 'readable', 'safe'],
        css_font_family: 'Arial, sans-serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false
      },
      {
        name: 'times',
        display_name: 'Times New Roman',
        category: 'serif',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['classic', 'formal', 'traditional'],
        css_font_family: 'Times, serif',
        is_google_font: false,
        is_system_font: true,
        is_premium: false
      },
      {
        name: 'courier',
        display_name: 'Courier New',
        category: 'monospace',
        variants: [{ weight: '400', style: 'normal' }, { weight: '700', style: 'normal' }],
        subsets: ['latin'],
        tags: ['monospace', 'code', 'technical'],
        css_font_family: 'Courier, monospace',
        is_google_font: false,
        is_system_font: true,
        is_premium: false
      },

      // Google Fonts
      {
        name: 'Inter',
        display_name: 'Inter',
        category: 'sans-serif',
        variants: [
          { weight: '400', style: 'normal' },
          { weight: '500', style: 'normal' },
          { weight: '600', style: 'normal' },
          { weight: '700', style: 'normal' }
        ],
        subsets: ['latin', 'latin-ext'],
        tags: ['modern', 'clean', 'readable', 'tech'],
        css_import_url: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
        css_font_family: 'Inter, sans-serif',
        is_google_font: true,
        is_system_font: false,
        is_premium: false
      },
      {
        name: 'Poppins',
        display_name: 'Poppins',
        category: 'sans-serif',
        variants: [
          { weight: '400', style: 'normal' },
          { weight: '500', style: 'normal' },
          { weight: '600', style: 'normal' },
          { weight: '700', style: 'normal' }
        ],
        subsets: ['latin', 'latin-ext'],
        tags: ['friendly', 'rounded', 'modern'],
        css_import_url: 'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap',
        css_font_family: 'Poppins, sans-serif',
        is_google_font: true,
        is_system_font: false,
        is_premium: false
      },
      {
        name: 'Playfair Display',
        display_name: 'Playfair Display',
        category: 'serif',
        variants: [
          { weight: '400', style: 'normal' },
          { weight: '700', style: 'normal' },
          { weight: '400', style: 'italic' }
        ],
        subsets: ['latin', 'latin-ext'],
        tags: ['elegant', 'luxury', 'fashion', 'editorial'],
        css_import_url: 'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400&display=swap',
        css_font_family: 'Playfair Display, serif',
        is_google_font: true,
        is_system_font: false,
        is_premium: false
      },
      {
        name: 'Roboto Mono',
        display_name: 'Roboto Mono',
        category: 'monospace',
        variants: [
          { weight: '400', style: 'normal' },
          { weight: '700', style: 'normal' }
        ],
        subsets: ['latin', 'latin-ext'],
        tags: ['tech', 'code', 'modern', 'clean'],
        css_import_url: 'https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&display=swap',
        css_font_family: 'Roboto Mono, monospace',
        is_google_font: true,
        is_system_font: false,
        is_premium: false
      },
      {
        name: 'Dancing Script',
        display_name: 'Dancing Script',
        category: 'cursive',
        variants: [
          { weight: '400', style: 'normal' },
          { weight: '700', style: 'normal' }
        ],
        subsets: ['latin', 'latin-ext'],
        tags: ['handwriting', 'casual', 'fun', 'script'],
        css_import_url: 'https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap',
        css_font_family: 'Dancing Script, cursive',
        is_google_font: true,
        is_system_font: false,
        is_premium: false
      },
      {
        name: 'Caveat',
        display_name: 'Caveat',
        category: 'cursive',
        variants: [
          { weight: '400', style: 'normal' },
          { weight: '700', style: 'normal' }
        ],
        subsets: ['latin', 'latin-ext'],
        tags: ['handwriting', 'casual', 'personal', 'brush'],
        css_import_url: 'https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&display=swap',
        css_font_family: 'Caveat, cursive',
        is_google_font: true,
        is_system_font: false,
        is_premium: false
      }
    ];

    for (const font of defaultFonts) {
      try {
        await this.createFont(font);
        console.log(`Created font: ${font.name}`);
      } catch (error) {
        console.error(`Failed to create font ${font.name}:`, error);
      }
    }
  }
}

// Export singleton instance
export const fontsService = new FontsService();
