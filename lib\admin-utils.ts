/**
 * Admin utilities for managing user data and cleanup operations
 * These functions should only be used by administrators
 */

import { supabase } from './supabase';
import { TABLES } from './supabase';
import { userService, UserProfile } from './user-service';

export class AdminUtils {
  // Find all duplicate users by email
  async findAllDuplicateUsers(): Promise<{ email: string; count: number; profiles: UserProfile[] }[]> {
    try {
      console.log('Scanning for duplicate users...');
      
      // Get all users
      const result = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        [Query.limit(1000)] // Adjust limit as needed
      );

      const users = result.documents as UserProfile[];
      
      // Group by email
      const emailGroups: { [email: string]: UserProfile[] } = {};
      
      users.forEach(user => {
        if (user.email) {
          if (!emailGroups[user.email]) {
            emailGroups[user.email] = [];
          }
          emailGroups[user.email].push(user);
        }
      });

      // Find duplicates
      const duplicates = Object.entries(emailGroups)
        .filter(([email, profiles]) => profiles.length > 1)
        .map(([email, profiles]) => ({
          email,
          count: profiles.length,
          profiles: profiles.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        }));

      console.log(`Found ${duplicates.length} emails with duplicate users`);
      
      return duplicates;
    } catch (error) {
      console.error('Failed to find duplicate users:', error);
      throw error;
    }
  }

  // Clean up all duplicate users
  async cleanupAllDuplicateUsers(): Promise<{
    processed: number;
    merged: number;
    deleted: number;
    errors: string[];
  }> {
    try {
      const duplicates = await this.findAllDuplicateUsers();
      const results = {
        processed: 0,
        merged: 0,
        deleted: 0,
        errors: [] as string[]
      };

      console.log(`Processing ${duplicates.length} duplicate email groups...`);

      for (const duplicate of duplicates) {
        try {
          results.processed++;
          
          const primaryProfile = duplicate.profiles[0]; // Oldest profile
          const duplicateProfiles = duplicate.profiles.slice(1);
          
          console.log(`Merging ${duplicateProfiles.length} duplicates for email: ${duplicate.email}`);
          
          // Delete duplicate profiles
          for (const profile of duplicateProfiles) {
            try {
              await userService.deleteUserProfile(profile.$id!);
              results.deleted++;
              console.log(`Deleted duplicate profile: ${profile.$id} (${profile.username})`);
            } catch (error) {
              const errorMsg = `Failed to delete profile ${profile.$id}: ${error}`;
              console.error(errorMsg);
              results.errors.push(errorMsg);
            }
          }
          
          results.merged++;
          console.log(`Kept primary profile: ${primaryProfile.$id} (${primaryProfile.username})`);
          
        } catch (error) {
          const errorMsg = `Failed to process duplicates for ${duplicate.email}: ${error}`;
          console.error(errorMsg);
          results.errors.push(errorMsg);
        }
      }

      console.log('Cleanup completed:', results);
      return results;
    } catch (error) {
      console.error('Failed to cleanup duplicate users:', error);
      throw error;
    }
  }

  // Get user statistics
  async getUserStats(): Promise<{
    totalUsers: number;
    publicUsers: number;
    usersWithDuplicateEmails: number;
    usersWithoutEmail: number;
    recentUsers: number; // Last 7 days
  }> {
    try {
      const allUsers = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        [Query.limit(1000)]
      );

      const users = allUsers.documents as UserProfile[];
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // Group by email to find duplicates
      const emailGroups: { [email: string]: number } = {};
      let usersWithoutEmail = 0;

      users.forEach(user => {
        if (user.email) {
          emailGroups[user.email] = (emailGroups[user.email] || 0) + 1;
        } else {
          usersWithoutEmail++;
        }
      });

      const usersWithDuplicateEmails = Object.values(emailGroups)
        .filter(count => count > 1)
        .reduce((sum, count) => sum + count, 0);

      const stats = {
        totalUsers: users.length,
        publicUsers: users.filter(user => user.isPublic).length,
        usersWithDuplicateEmails,
        usersWithoutEmail,
        recentUsers: users.filter(user => 
          new Date(user.createdAt) > sevenDaysAgo
        ).length
      };

      console.log('User Statistics:', stats);
      return stats;
    } catch (error) {
      console.error('Failed to get user stats:', error);
      throw error;
    }
  }

  // Validate user data integrity
  async validateUserDataIntegrity(): Promise<{
    valid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const issues: string[] = [];
      const recommendations: string[] = [];

      // Check for duplicate emails
      const duplicates = await this.findAllDuplicateUsers();
      if (duplicates.length > 0) {
        issues.push(`Found ${duplicates.length} emails with duplicate users`);
        recommendations.push('Run cleanupAllDuplicateUsers() to merge duplicates');
      }

      // Check for users without email
      const stats = await this.getUserStats();
      if (stats.usersWithoutEmail > 0) {
        issues.push(`Found ${stats.usersWithoutEmail} users without email addresses`);
        recommendations.push('Review users without emails and update them manually');
      }

      // Check for users without usernames
      const allUsers = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        [Query.limit(1000)]
      );

      const usersWithoutUsername = allUsers.documents.filter(
        user => !user.username || user.username.trim() === ''
      ).length;

      if (usersWithoutUsername > 0) {
        issues.push(`Found ${usersWithoutUsername} users without usernames`);
        recommendations.push('Generate usernames for users missing them');
      }

      const result = {
        valid: issues.length === 0,
        issues,
        recommendations
      };

      console.log('Data Integrity Check:', result);
      return result;
    } catch (error) {
      console.error('Failed to validate user data integrity:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const adminUtils = new AdminUtils();

// Helper function to run cleanup (can be called from browser console)
export async function runDuplicateCleanup() {
  console.log('Starting duplicate user cleanup...');
  try {
    const results = await adminUtils.cleanupAllDuplicateUsers();
    console.log('Cleanup completed successfully:', results);
    return results;
  } catch (error) {
    console.error('Cleanup failed:', error);
    throw error;
  }
}

// Helper function to check data integrity
export async function checkDataIntegrity() {
  console.log('Checking user data integrity...');
  try {
    const results = await adminUtils.validateUserDataIntegrity();
    console.log('Integrity check completed:', results);
    return results;
  } catch (error) {
    console.error('Integrity check failed:', error);
    throw error;
  }
}
