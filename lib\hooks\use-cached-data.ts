import { useEffect, useState } from 'react';
import { useAuthStore } from '../stores/auth-store';
import { useLinksStore } from '../stores/links-store';
import { useProfileStore } from '../stores/profile-store';
import { useFontsStore } from '../stores/fonts-store';
import { useThemeStore } from '../stores/theme-store';
import { useCacheStore } from '../stores/cache-store';

// Hook for loading user's complete data set
export const useUserData = (forceRefresh = false) => {
  const { user } = useAuthStore();
  const linksStore = useLinksStore();
  const profileStore = useProfileStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user?.id) return;

    const loadUserData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Load profile and links in parallel
        await Promise.all([
          profileStore.loadProfile(user.id, forceRefresh),
          linksStore.loadLinks(user.id, forceRefresh)
        ]);
      } catch (err) {
        console.error('Failed to load user data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, [user?.id, forceRefresh]);

  return {
    profile: profileStore.profile,
    links: linksStore.links,
    isLoading: isLoading || profileStore.isLoading || linksStore.isLoading,
    error,
    refresh: () => {
      if (user?.id) {
        profileStore.loadProfile(user.id, true);
        linksStore.loadLinks(user.id, true);
      }
    }
  };
};

// Hook for loading public profile data
export const usePublicData = (username: string, forceRefresh = false) => {
  const profileStore = useProfileStore();
  const [publicLinks, setPublicLinks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!username) return;

    const loadPublicData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Load public profile and links
        const profile = await profileStore.loadPublicProfile(username, forceRefresh);
        if (profile) {
          // Load public links for this profile
          // This would need to be implemented in the cached API
          // const links = await cachedAPI.getPublicLinks(username, forceRefresh);
          // setPublicLinks(links);
        }
      } catch (err) {
        console.error('Failed to load public data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    loadPublicData();
  }, [username, forceRefresh]);

  return {
    profile: profileStore.publicProfiles[username],
    links: publicLinks,
    isLoading,
    error,
    refresh: () => profileStore.loadPublicProfile(username, true)
  };
};

// Hook for loading static data (fonts, themes, etc.)
export const useStaticData = (forceRefresh = false) => {
  const fontsStore = useFontsStore();
  const themeStore = useThemeStore();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const loadStaticData = async () => {
      setIsLoading(true);

      try {
        // Load fonts and default themes in parallel
        await Promise.all([
          fontsStore.loadPopularFonts(20, forceRefresh),
          fontsStore.loadDefaultFonts(forceRefresh),
          themeStore.loadDefaultThemes(forceRefresh)
        ]);
      } catch (error) {
        console.error('Failed to load static data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Only load if we don't have data or force refresh
    if (forceRefresh || 
        fontsStore.popularFonts.length === 0 || 
        fontsStore.defaultFonts.length === 0 ||
        themeStore.defaultThemes.length === 0) {
      loadStaticData();
    }
  }, [forceRefresh]);

  return {
    fonts: {
      popular: fontsStore.popularFonts,
      default: fontsStore.defaultFonts,
      all: fontsStore.allFonts
    },
    themes: {
      default: themeStore.defaultThemes,
      public: themeStore.publicThemes
    },
    isLoading: isLoading || fontsStore.isLoading || themeStore.isLoading
  };
};

// Hook for smart data refresh based on visibility
export const useVisibilityRefresh = (refreshCallback: () => void, interval = 5 * 60 * 1000) => {
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh data
        refreshCallback();
      }
    };

    const startInterval = () => {
      intervalId = setInterval(() => {
        if (!document.hidden) {
          refreshCallback();
        }
      }, interval);
    };

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Start periodic refresh
    startInterval();

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [refreshCallback, interval]);
};

// Hook for optimistic updates
export const useOptimisticUpdate = <T>(
  data: T[],
  updateFn: (id: string, updates: Partial<T>) => Promise<T>,
  idKey: keyof T = 'id' as keyof T
) => {
  const [optimisticData, setOptimisticData] = useState(data);

  useEffect(() => {
    setOptimisticData(data);
  }, [data]);

  const updateOptimistically = async (id: string, updates: Partial<T>) => {
    // Apply optimistic update
    setOptimisticData(prev => 
      prev.map(item => 
        item[idKey] === id ? { ...item, ...updates } : item
      )
    );

    try {
      // Perform actual update
      const updatedItem = await updateFn(id, updates);
      
      // Update with real data
      setOptimisticData(prev => 
        prev.map(item => 
          item[idKey] === id ? updatedItem : item
        )
      );

      return updatedItem;
    } catch (error) {
      // Revert optimistic update on error
      setOptimisticData(data);
      throw error;
    }
  };

  return {
    data: optimisticData,
    updateOptimistically
  };
};

// Hook for debounced saves
export const useDebouncedSave = <T>(
  value: T,
  saveFn: (value: T) => Promise<void>,
  delay = 1000
) => {
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    setHasUnsavedChanges(true);
    
    const timeoutId = setTimeout(async () => {
      setIsSaving(true);
      try {
        await saveFn(value);
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error('Failed to save:', error);
      } finally {
        setIsSaving(false);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [value, saveFn, delay]);

  return {
    isSaving,
    hasUnsavedChanges
  };
};

// Hook for cache statistics and management
export const useCacheManagement = () => {
  const cacheStore = useCacheStore();
  const getCacheStats = cacheStore.getStats;
  const clearAllCache = cacheStore.clear;
  const [stats, setStats] = useState(getCacheStats());

  const refreshStats = () => {
    setStats(getCacheStats());
  };

  useEffect(() => {
    // Refresh stats every 30 seconds
    const interval = setInterval(refreshStats, 30000);
    return () => clearInterval(interval);
  }, []);

  return {
    stats,
    refreshStats,
    clearCache: clearAllCache
  };
};
