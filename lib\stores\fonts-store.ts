import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { cachedAPI } from '../cached-api';
import { Font } from '../fonts-service';

interface FontsState {
  // Data
  allFonts: Font[];
  popularFonts: Font[];
  defaultFonts: Font[];
  fontsByCategory: Record<string, Font[]>;
  searchResults: Record<string, Font[]>;
  
  // State
  isLoading: boolean;
  lastFetch: number | null;
  selectedFont: Font | null;
  
  // Actions
  loadAllFonts: (forceRefresh?: boolean) => Promise<void>;
  loadPopularFonts: (limit?: number, forceRefresh?: boolean) => Promise<void>;
  loadDefaultFonts: (forceRefresh?: boolean) => Promise<void>;
  loadFontsByCategory: (category: string, forceRefresh?: boolean) => Promise<void>;
  searchFonts: (query: string, forceRefresh?: boolean) => Promise<Font[]>;
  setSelectedFont: (font: Font | null) => void;
  incrementFontUsage: (fontId: string) => Promise<void>;
  
  // Getters
  getFontsByCategory: (category: string) => Font[];
  getSearchResults: (query: string) => Font[];
  clearCache: () => void;
}

export const useFontsStore = create<FontsState>()(
  persist(
    (set, get) => ({
      // Initial state
      allFonts: [],
      popularFonts: [],
      defaultFonts: [],
      fontsByCategory: {},
      searchResults: {},
      isLoading: false,
      lastFetch: null,
      selectedFont: null,

      loadAllFonts: async (forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 60 * 60 * 1000; // 1 hour for fonts

        if (!forceRefresh && state.lastFetch && 
            state.allFonts.length > 0 &&
            (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Fonts: Using cached all fonts');
          return;
        }

        set({ isLoading: true });
        try {
          console.log('🔄 Loading all fonts');
          const fonts = await cachedAPI.getAllFonts(forceRefresh);
          
          set({
            allFonts: fonts,
            isLoading: false,
            lastFetch: now
          });
          
          console.log('✅ All fonts loaded:', fonts.length);
        } catch (error) {
          console.error('❌ Failed to load all fonts:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      loadPopularFonts: async (limit = 20, forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

        if (!forceRefresh && state.popularFonts.length > 0 &&
            state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Fonts: Using cached popular fonts');
          return;
        }

        set({ isLoading: true });
        try {
          console.log('🔄 Loading popular fonts');
          const fonts = await cachedAPI.getPopularFonts(limit, forceRefresh);
          
          set({
            popularFonts: fonts,
            isLoading: false
          });
          
          console.log('✅ Popular fonts loaded:', fonts.length);
        } catch (error) {
          console.error('❌ Failed to load popular fonts:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      loadDefaultFonts: async (forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 2 * 60 * 60 * 1000; // 2 hours

        if (!forceRefresh && state.defaultFonts.length > 0 &&
            state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          console.log('📦 Fonts: Using cached default fonts');
          return;
        }

        set({ isLoading: true });
        try {
          console.log('🔄 Loading default fonts');
          const fonts = await cachedAPI.getDefaultFonts(forceRefresh);
          
          set({
            defaultFonts: fonts,
            isLoading: false
          });
          
          console.log('✅ Default fonts loaded:', fonts.length);
        } catch (error) {
          console.error('❌ Failed to load default fonts:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      loadFontsByCategory: async (category: string, forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 60 * 60 * 1000; // 1 hour

        const cachedFonts = state.fontsByCategory[category];
        if (!forceRefresh && cachedFonts && cachedFonts.length > 0 &&
            state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          console.log(`📦 Fonts: Using cached fonts for category: ${category}`);
          return;
        }

        set({ isLoading: true });
        try {
          console.log(`🔄 Loading fonts for category: ${category}`);
          const fonts = await cachedAPI.getFontsByCategory(category, forceRefresh);
          
          set((state) => ({
            fontsByCategory: {
              ...state.fontsByCategory,
              [category]: fonts
            },
            isLoading: false
          }));
          
          console.log(`✅ Fonts loaded for category ${category}:`, fonts.length);
        } catch (error) {
          console.error(`❌ Failed to load fonts for category ${category}:`, error);
          set({ isLoading: false });
          throw error;
        }
      },

      searchFonts: async (query: string, forceRefresh = false) => {
        const state = get();
        const cachedResults = state.searchResults[query];
        
        if (!forceRefresh && cachedResults) {
          console.log(`📦 Fonts: Using cached search results for: ${query}`);
          return cachedResults;
        }

        set({ isLoading: true });
        try {
          console.log(`🔍 Searching fonts: ${query}`);
          const fonts = await cachedAPI.searchFonts(query, forceRefresh);
          
          set((state) => ({
            searchResults: {
              ...state.searchResults,
              [query]: fonts
            },
            isLoading: false
          }));
          
          console.log(`✅ Font search results for "${query}":`, fonts.length);
          return fonts;
        } catch (error) {
          console.error(`❌ Failed to search fonts for "${query}":`, error);
          set({ isLoading: false });
          throw error;
        }
      },

      setSelectedFont: (font) => {
        set({ selectedFont: font });
      },

      incrementFontUsage: async (fontId: string) => {
        try {
          // This would typically call the fonts service
          // For now, we'll just log it
          console.log('📈 Incrementing font usage:', fontId);
          // await fontsService.incrementUsage(fontId);
        } catch (error) {
          console.error('❌ Failed to increment font usage:', error);
        }
      },

      // Getters
      getFontsByCategory: (category: string) => {
        return get().fontsByCategory[category] || [];
      },

      getSearchResults: (query: string) => {
        return get().searchResults[query] || [];
      },

      clearCache: () => {
        set({
          allFonts: [],
          popularFonts: [],
          defaultFonts: [],
          fontsByCategory: {},
          searchResults: {},
          lastFetch: null
        });
      },
    }),
    {
      name: 'linkvibe-fonts-storage',
      partialize: (state) => ({
        allFonts: state.allFonts,
        popularFonts: state.popularFonts,
        defaultFonts: state.defaultFonts,
        fontsByCategory: state.fontsByCategory,
        searchResults: state.searchResults,
        lastFetch: state.lastFetch,
        selectedFont: state.selectedFont,
        // Don't persist loading state
      }),
      storage: typeof window !== 'undefined' ? localStorage : undefined,
    }
  )
);
