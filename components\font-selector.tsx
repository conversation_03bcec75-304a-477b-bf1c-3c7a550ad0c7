'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { fontsService, Font } from '@/lib/fonts-service';
import {
  Search,
  Monitor,
  Globe,
  Type
} from 'lucide-react';

interface FontSelectorProps {
  selectedFont?: string;
  onFontSelect: (fontFamily: string, fontName: string) => void;
  previewText?: string;
}

export function FontSelector({
  selectedFont,
  onFontSelect,
  previewText = "The quick brown fox jumps over the lazy dog"
}: FontSelectorProps) {
  const [systemFonts, setSystemFonts] = useState<Font[]>([]);
  const [googleFonts, setGoogleFonts] = useState<Font[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingSystem, setIsLoadingSystem] = useState(true);
  const [isLoadingGoogle, setIsLoadingGoogle] = useState(false);

  useEffect(() => {
    loadSystemFonts();
  }, []);

  const loadSystemFonts = async () => {
    setIsLoadingSystem(true);
    try {
      const systemFonts = await fontsService.getDefaultFonts();
      setSystemFonts(systemFonts);
    } catch (error) {
      console.error('Failed to load system fonts:', error);
    } finally {
      setIsLoadingSystem(false);
    }
  };

  const loadGoogleFonts = async () => {
    if (googleFonts.length > 0) return; // Already loaded

    setIsLoadingGoogle(true);
    try {
      const gFonts = await fontsService.getGoogleFonts(100);
      setGoogleFonts(gFonts);
    } catch (error) {
      console.error('Failed to load Google Fonts:', error);
    } finally {
      setIsLoadingGoogle(false);
    }
  };

  const handleFontSelect = async (font: Font) => {
    try {
      // Load Google Font if needed
      if (font.is_google_font && font.name) {
        fontsService.loadGoogleFont(font.name, font.variants);
      }

      // Increment usage count (only for database fonts)
      if (font.id) {
        await fontsService.incrementUsage(font.id);
      }

      onFontSelect(font.css_font_family, font.display_name);
    } catch (error) {
      console.error('Failed to select font:', error);
    }
  };

  const filteredGoogleFonts = googleFonts.filter(font => {
    if (!searchQuery) return true;
    return font.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           font.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
  });

  const FontCard = ({ font, index }: { font: Font; index: number }) => {
    const isSelected = selectedFont === font.css_font_family;

    return (
      <Card
        className={`cursor-pointer transition-all hover:shadow-md ${
          isSelected ? 'ring-2 ring-purple-500 bg-purple-50' : ''
        }`}
        onClick={() => handleFontSelect(font)}
      >
        <CardContent className="p-4">
          <div className="space-y-3">
            {/* Font Name and Category */}
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-sm">{font.display_name}</h3>
                <p className="text-xs text-muted-foreground capitalize">{font.category}</p>
              </div>
              <div className="flex gap-1">
                {font.is_google_font && (
                  <Badge variant="secondary" className="text-xs">
                    <Globe className="w-3 h-3 mr-1" />
                    Google
                  </Badge>
                )}
                {font.is_system_font && (
                  <Badge variant="outline" className="text-xs">
                    <Monitor className="w-3 h-3 mr-1" />
                    System
                  </Badge>
                )}
              </div>
            </div>

            {/* Font Preview */}
            <div
              className="text-lg leading-relaxed"
              style={{ fontFamily: font.css_font_family }}
            >
              {previewText.slice(0, 30)}...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoadingSystem) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Type className="w-5 h-5 mr-2" />
            Font Selector
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading fonts...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Type className="w-5 h-5 mr-2" />
          Font Selector
        </CardTitle>
        <CardDescription>
          Choose the perfect font for your bio page
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search fonts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Font Tabs */}
        <Tabs defaultValue="system" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="system">
              <Monitor className="w-4 h-4 mr-2" />
              System Fonts
            </TabsTrigger>
            <TabsTrigger value="google" onClick={loadGoogleFonts}>
              <Globe className="w-4 h-4 mr-2" />
              Google Fonts
            </TabsTrigger>
          </TabsList>

          <TabsContent value="system" className="space-y-3">
            <div className="grid gap-3 max-h-96 overflow-y-auto">
              {systemFonts.map((font, index) => (
                <FontCard key={font.id || `system-${index}`} font={font} index={index} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="google" className="space-y-3">
            {isLoadingGoogle ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading Google Fonts...</p>
                </div>
              </div>
            ) : (
              <div className="grid gap-3 max-h-96 overflow-y-auto">
                {filteredGoogleFonts.map((font, index) => (
                  <FontCard key={font.id || `google-${index}`} font={font} index={index} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Selected Font Info */}
        {selectedFont && (
          <div className="mt-4 p-3 bg-purple-50 rounded-lg">
            <p className="text-sm font-medium">Selected Font:</p>
            <p className="text-sm text-muted-foreground" style={{ fontFamily: selectedFont }}>
              {previewText}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
