'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sparkles, Zap, Camera, Palette, Film, Clock, Cloud } from 'lucide-react';

interface BackgroundEffectsProps {
  currentEffect: string;
  onEffectChange: (effect: string) => void;
  disabled?: boolean;
}

const effects = [
  {
    id: 'none',
    name: 'None',
    description: 'Original image without effects',
    icon: <Camera className="w-4 h-4" />,
    preview: 'No effects applied',
    isPremium: false
  },
  {
    id: 'motion-blur',
    name: 'Motion Blur',
    description: 'Dynamic motion blur effect',
    icon: <Zap className="w-4 h-4" />,
    preview: 'Adds movement and energy',
    isPremium: false
  },
  {
    id: 'faded-sides',
    name: 'Faded Sides',
    description: 'Soft vignette with faded edges',
    icon: <Cloud className="w-4 h-4" />,
    preview: 'Gentle fade to focus center',
    isPremium: false
  },
  {
    id: 'aesthetic-vibe',
    name: 'Aesthetic Vibe',
    description: 'Instagram-style aesthetic filter',
    icon: <Sparkles className="w-4 h-4" />,
    preview: 'Trendy social media look',
    isPremium: true
  },
  {
    id: 'cinematic',
    name: 'Cinematic',
    description: 'Movie-like cinematic effects',
    icon: <Film className="w-4 h-4" />,
    preview: 'Professional film quality',
    isPremium: true
  },
  {
    id: 'vintage',
    name: 'Vintage',
    description: 'Retro film grain and color',
    icon: <Clock className="w-4 h-4" />,
    preview: 'Classic vintage photography',
    isPremium: true
  },
  {
    id: 'dreamy',
    name: 'Dreamy',
    description: 'Soft, ethereal glow effect',
    icon: <Palette className="w-4 h-4" />,
    preview: 'Magical dreamy atmosphere',
    isPremium: true
  }
];

export default function BackgroundEffects({ 
  currentEffect, 
  onEffectChange, 
  disabled = false 
}: BackgroundEffectsProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Sparkles className="w-5 h-5" />
          <span>Background Effects</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-3">
          {effects.map((effect) => (
            <div
              key={effect.id}
              className={`
                relative p-3 rounded-lg border-2 cursor-pointer transition-all duration-200
                ${currentEffect === effect.id 
                  ? 'border-purple-500 bg-purple-50' 
                  : 'border-gray-200 hover:border-gray-300 bg-white'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => !disabled && onEffectChange(effect.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className={`
                    p-2 rounded-lg 
                    ${currentEffect === effect.id 
                      ? 'bg-purple-100 text-purple-600' 
                      : 'bg-gray-100 text-gray-600'
                    }
                  `}>
                    {effect.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-gray-900">{effect.name}</h3>
                      {effect.isPremium && (
                        <Badge variant="secondary" className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                          Premium
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{effect.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{effect.preview}</p>
                  </div>
                </div>
                
                {currentEffect === effect.id && (
                  <div className="w-4 h-4 rounded-full bg-purple-500 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-white" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start space-x-2">
            <Sparkles className="w-4 h-4 text-blue-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-blue-900">Pro Tip</p>
              <p className="text-xs text-blue-700 mt-1">
                Effects work best with high-quality images. Try different effects to find your perfect aesthetic!
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
