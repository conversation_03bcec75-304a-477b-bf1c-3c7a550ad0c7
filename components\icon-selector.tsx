"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Youtube, 
  Instagram, 
  Twitter, 
  Facebook, 
  Linkedin, 
  Github,
  Globe,
  Mail,
  Phone,
  MapPin,
  ShoppingBag,
  Camera,
  Music,
  Video,
  BookOpen,
  Coffee,
  Heart,
  Star,
  Zap,
  Rocket,
  Palette,
  Code,
  Briefcase,
  GraduationCap,
  Home,
  User,
  Settings,
  Search,
  Plus,
  Download,
  Upload,
  Edit,
  Trash,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Bell,
  Calendar,
  Clock,
  Flag,
  Gift,
  Headphones,
  Image,
  Link,
  MessageCircle,
  Mic,
  Monitor,
  Smartphone,
  Tablet,
  Wifi,
  Bluetooth,
  Battery,
  Volume2,
  Play,
  Pause,
  SkipFor<PERSON>,
  Ski<PERSON><PERSON><PERSON>,
  Repeat,
  Shuffle
} from "lucide-react";

interface IconSelectorProps {
  selectedIcon: string;
  onIconSelect: (icon: string) => void;
  trigger?: React.ReactNode;
}

export function IconSelector({ selectedIcon, onIconSelect, trigger }: IconSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("social");

  const iconCategories = {
    social: {
      name: "Social Media",
      icons: [
        { name: "YouTube", component: Youtube, value: "youtube" },
        { name: "Instagram", component: Instagram, value: "instagram" },
        { name: "Twitter", component: Twitter, value: "twitter" },
        { name: "Facebook", component: Facebook, value: "facebook" },
        { name: "LinkedIn", component: Linkedin, value: "linkedin" },
        { name: "GitHub", component: Github, value: "github" },
        { name: "TikTok", component: Music, value: "tiktok" },
        { name: "Discord", component: MessageCircle, value: "discord" }
      ]
    },
    business: {
      name: "Business",
      icons: [
        { name: "Website", component: Globe, value: "globe" },
        { name: "Email", component: Mail, value: "mail" },
        { name: "Phone", component: Phone, value: "phone" },
        { name: "Location", component: MapPin, value: "map-pin" },
        { name: "Shop", component: ShoppingBag, value: "shopping-bag" },
        { name: "Briefcase", component: Briefcase, value: "briefcase" },
        { name: "Calendar", component: Calendar, value: "calendar" },
        { name: "Clock", component: Clock, value: "clock" }
      ]
    },
    creative: {
      name: "Creative",
      icons: [
        { name: "Camera", component: Camera, value: "camera" },
        { name: "Music", component: Music, value: "music" },
        { name: "Video", component: Video, value: "video" },
        { name: "Palette", component: Palette, value: "palette" },
        { name: "Book", component: BookOpen, value: "book-open" },
        { name: "Code", component: Code, value: "code" },
        { name: "Image", component: Image, value: "image" },
        { name: "Mic", component: Mic, value: "mic" }
      ]
    },
    lifestyle: {
      name: "Lifestyle",
      icons: [
        { name: "Coffee", component: Coffee, value: "coffee" },
        { name: "Heart", component: Heart, value: "heart" },
        { name: "Star", component: Star, value: "star" },
        { name: "Gift", component: Gift, value: "gift" },
        { name: "Home", component: Home, value: "home" },
        { name: "Graduation", component: GraduationCap, value: "graduation-cap" },
        { name: "Headphones", component: Headphones, value: "headphones" },
        { name: "Flag", component: Flag, value: "flag" }
      ]
    },
    tech: {
      name: "Technology",
      icons: [
        { name: "Monitor", component: Monitor, value: "monitor" },
        { name: "Smartphone", component: Smartphone, value: "smartphone" },
        { name: "Tablet", component: Tablet, value: "tablet" },
        { name: "Wifi", component: Wifi, value: "wifi" },
        { name: "Bluetooth", component: Bluetooth, value: "bluetooth" },
        { name: "Battery", component: Battery, value: "battery" },
        { name: "Download", component: Download, value: "download" },
        { name: "Upload", component: Upload, value: "upload" }
      ]
    },
    actions: {
      name: "Actions",
      icons: [
        { name: "Play", component: Play, value: "play" },
        { name: "Pause", component: Pause, value: "pause" },
        { name: "Zap", component: Zap, value: "zap" },
        { name: "Rocket", component: Rocket, value: "rocket" },
        { name: "Plus", component: Plus, value: "plus" },
        { name: "Edit", component: Edit, value: "edit" },
        { name: "Search", component: Search, value: "search" },
        { name: "Link", component: Link, value: "link" }
      ]
    }
  };

  const getAllIcons = () => {
    return Object.values(iconCategories).flatMap(category => category.icons);
  };

  const getFilteredIcons = () => {
    const icons = selectedCategory === "all" ? getAllIcons() : iconCategories[selectedCategory as keyof typeof iconCategories]?.icons || [];
    
    if (!searchTerm) return icons;
    
    return icons.filter(icon => 
      icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      icon.value.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const getIconComponent = (iconValue: string) => {
    const allIcons = getAllIcons();
    const icon = allIcons.find(i => i.value === iconValue);
    return icon ? icon.component : Globe;
  };

  const SelectedIconComponent = getIconComponent(selectedIcon);

  return (
    <Popover>
      <PopoverTrigger asChild>
        {trigger || (
          <Button variant="outline" className="w-full justify-start">
            <SelectedIconComponent className="w-4 h-4 mr-2" />
            {getAllIcons().find(i => i.value === selectedIcon)?.name || "Select Icon"}
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="start">
        <div className="p-4 border-b">
          <Label className="text-sm font-semibold">Choose an Icon</Label>
          <Input
            placeholder="Search icons..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="mt-2"
          />
        </div>

        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-3 rounded-none border-b">
            <TabsTrigger value="social" className="text-xs">Social</TabsTrigger>
            <TabsTrigger value="business" className="text-xs">Business</TabsTrigger>
            <TabsTrigger value="creative" className="text-xs">Creative</TabsTrigger>
          </TabsList>
          <TabsList className="grid w-full grid-cols-3 rounded-none border-b">
            <TabsTrigger value="lifestyle" className="text-xs">Lifestyle</TabsTrigger>
            <TabsTrigger value="tech" className="text-xs">Tech</TabsTrigger>
            <TabsTrigger value="actions" className="text-xs">Actions</TabsTrigger>
          </TabsList>

          <div className="max-h-64 overflow-y-auto p-4">
            <div className="grid grid-cols-4 gap-2">
              {getFilteredIcons().map((icon) => {
                const IconComponent = icon.component;
                return (
                  <button
                    key={icon.value}
                    onClick={() => onIconSelect(icon.value)}
                    className={`p-3 rounded-lg border-2 transition-all hover:bg-gray-50 ${
                      selectedIcon === icon.value
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200'
                    }`}
                    title={icon.name}
                  >
                    <IconComponent className="w-5 h-5 mx-auto" />
                  </button>
                );
              })}
            </div>

            {getFilteredIcons().length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No icons found</p>
              </div>
            )}
          </div>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}
